Object.defineProperty(exports,"__esModule",{value:!0});var React=require("react"),data=require("@wordpress/data"),lodashEs=require("lodash-es"),apiFetch=require("@wordpress/api-fetch"),date=require("@wordpress/date"),moment=require("moment"),intersect=require("intersect"),i18n=require("@wordpress/i18n"),classNames=require("classnames"),hooks=require("@wordpress/hooks"),Swal=require("sweetalert2"),components=require("@wordpress/components"),copy=require("copy-to-clipboard"),ReactSelect=require("react-select"),reactSortablejs=require("react-sortablejs"),parse=require("html-react-parser"),AsyncSelect=require("react-select/async"),mediaUtils=require("@wordpress/media-utils"),reactDraftWysiwyg=require("react-draft-wysiwyg"),draftJs=require("draft-js"),draftToHtml=require("draftjs-to-html"),htmlToDraft=require("html-to-draftjs"),SweetAlert$1=(require("react-draft-wysiwyg/dist/react-draft-wysiwyg.css"),require("react-bootstrap-sweetalert"));function _interopDefaultLegacy(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var React__default=_interopDefaultLegacy(React),apiFetch__default=_interopDefaultLegacy(apiFetch),moment__default=_interopDefaultLegacy(moment),intersect__default=_interopDefaultLegacy(intersect),classNames__default=_interopDefaultLegacy(classNames),Swal__default=_interopDefaultLegacy(Swal),copy__default=_interopDefaultLegacy(copy),ReactSelect__default=_interopDefaultLegacy(ReactSelect),parse__default=_interopDefaultLegacy(parse),AsyncSelect__default=_interopDefaultLegacy(AsyncSelect),draftToHtml__default=_interopDefaultLegacy(draftToHtml),htmlToDraft__default=_interopDefaultLegacy(htmlToDraft),SweetAlert__default=_interopDefaultLegacy(SweetAlert$1);function _typeof$1(e){return(_typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function toPrimitive(e,t){if("object"!=_typeof$1(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof$1(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}function toPropertyKey(e){e=toPrimitive(e,"string");return"symbol"==_typeof$1(e)?e:e+""}function _defineProperty(e,t,n){return(t=toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _arrayLikeToArray$2(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray$2(e)}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _unsupportedIterableToArray$2(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$2(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$2(e,t):void 0}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray$2(e)||_nonIterableSpread()}function ownKeys$g(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$g(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$g(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var wpFetch=function(e){e=_objectSpread$g(_objectSpread$g({},e),{},{method:"POST"});return apiFetch__default.default(e)},isString=function(e){return null!==e&&"string"==typeof e},isNumber=function(e){return null!==e&&"number"==typeof e},isInteger=function(e){return String(Math.floor(Number(e)))===e},isFunction=function(e){return null!==e&&"function"==typeof e},isArray=function(e){return null!==e&&"object"===_typeof$1(e)&&Array.isArray(e)},isObject=function(e){return null!==e&&"object"===_typeof$1(e)&&!isArray(e)},isVisible=function(e,t){return null==t||!t.rules||null==t.name||(t=when(t.rules,e),Boolean(t))},withState=function(e){return Boolean(["group","section"].includes(e))},isEmptyObj=function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},getIn=function(e,t,n,r){void 0===r&&(r=0);for(var a=lodashEs.toPath(t);e&&r<a.length;)e=e[a[r++]];return void 0===e?n:e},sortingFields=function(e){return[].concat(e).sort(function(e,t){return null==e.priority||null==t.priority?0:e.priority>t.priority?1:-1})},getSelectedValues=function(e){return Array.from(e).filter(function(e){return e.selected}).map(function(e){return e.value})},executeChange=function(e,t){var n=t,r=e;if(!isString(e)){e.persist&&e.persist();var e=e.target||e.currentTarget,a=e.type,l=e.value,o=e.checked,i=e.multiple,n=t||e.name;switch(a){case"number":case"range":r=parseFloat(l);break;case"checkbox":r=i?l:o;break;default:r=l}}return{field:n,val:r}},objectWithoutPropertiesLoose=function(e,t){if(null==e)return{};for(var n,r={},a=Object.keys(e),l=0;l<a.length;l++)n=a[l],0<=t.indexOf(n)||(r[n]=e[n]);return r},setIn=function(e,t,n){for(var r=lodashEs.clone(e),a=r,l=0,o=lodashEs.toPath(t);l<o.length-1;l++){var i=o[l],c=getIn(e,o.slice(0,l+1));a=c&&(isObject(c)||Array.isArray(c))?a[i]=lodashEs.clone(c):(c=o[l+1],a[i]=isInteger(c)&&0<=Number(c)?[]:{})}return(0===l?e:a)[o[l]]===n?e:(void 0===n?delete a[o[l]]:a[o[l]]=n,0===l&&void 0===n&&delete r[o[l]],r)},validFieldProps=function(e){var t=e.type,n=["validation_rules","default","rules","meta","switch"].concat(_toConsumableArray(1<arguments.length&&void 0!==arguments[1]?arguments[1]:[])),t=("select"!==t&&"select-async"!==t&&"radio-card"!==t&&"checkbox"!==t&&"toggle"!==t&&e.multiple&&n.push("options"),"tab"!==t&&"group"!==t&&"repeater"!==t&&"section"!==t&&"button"!==t&&n.push("fields"),objectWithoutPropertiesLoose(e,n));return null==e||!e.label||null!=e&&e.placeholder||(t.placeholder=e.label),t},hitAAJX=function(a){var l=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(null!==l&&a){var r,e=!0;if(e=null!=a&&a.rules?when(null==a?void 0:a.rules,l.values):e)return r={},Object.keys(a.data).map(function(e){var t,n;-1<(null==(n=(t=a.data[e]).indexOf)?void 0:n.call(t,"@"))?(n=a.data[e].substr(1),r[e]=null==(t=l.values)?void 0:t[n]):r[e]=a.data[e]}),wpFetch({path:a.api,data:r}).then(function(e){"success"==(null==e?void 0:e.status)&&null!=e&&e.redirect&&(window.location=null==e?void 0:e.redirect);var t,n,r=null!=e&&null!=(t=e.data)&&t.context?e.data.context:!(null==e||!e.context)&&e.context;return r&&isObject(r)&&Object.keys(r).map(function(e){l.setFieldValue(e,r[e])}),null!=e&&null!=(t=e.data)&&t.download&&downloadFile({data:JSON.stringify(e.data.download),fileName:(null==e||null==(t=e.data)?void 0:t.filename)||"export.json",fileType:"text/json"}),null!=a&&a.trigger&&isString(null==a?void 0:a.trigger)&&(t=a.trigger.indexOf("@"),n=a.trigger.indexOf(":"),0===t)&&0<n&&(t=a.trigger.substr(1,n-1),"true"==(n=a.trigger.substr(n+1))?n=!0:"false"==n&&(n=!1),l.setFieldValue(t,n)),e})}return Promise.reject(!1)},getTime=function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=date.__experimentalGetSettings();return moment__default.default.utc(e||void 0).utcOffset(+(null==n||null==(e=n.timezone)?void 0:e.offset),t)},merge=function(e,t,n){var r=_toConsumableArray(e),e=t.filter(function(t){return r.findIndex(function(e){return e[n]===t[n]})<=-1});return[].concat(_toConsumableArray(r),_toConsumableArray(e))},downloadFile=function(e){var t=e.data,n=e.fileName,t=new Blob([t],{type:e.fileType}),e=document.createElement("a"),n=(e.download=n,e.href=window.URL.createObjectURL(t),new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}));e.dispatchEvent(n),e.remove()},_typeof=function(e){return("function"!=typeof Symbol||"symbol"!==_typeof$1(Symbol.iterator))&&e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof$1(e)},get=function(e,t){var n=!(2<(arguments.length<=2?0:arguments.length-2)&&void 0!==(arguments.length<=4?void 0:arguments[4]))||arguments.length<=4?void 0:arguments[4];return String.prototype.split.call(t,/[,[\].]+?/).filter(Boolean).reduce(function(e,t){return e&&Object.hasOwnProperty.call(e,t)?e[t]:n},e)},rules={is:function(e,t,n){return get(n,e)==t},"!is":function(e,t,n){return!rules.is(e,t,n)},includes:function(e,t,n){if(!isEmptyObj(n)){n=get(n,e);if("function"!=_typeof(n)){if(isArray(t)&&isArray(n))return!(null==(e=intersect__default.default(n,t))||!e.length);if(isArray(t)&&"string"==_typeof(n))return t.includes(n);if(isArray(n)&&"string"==_typeof(t))return n.includes(t)}}return!1},"!includes":function(e,t,n){return!rules.includes(e,t,n)},isOfType:function(e,t,n){return _typeof(get(n,e))===t},"!isOfType":function(e,t,n){return!rules.isOfType(e,t,n)},allOf:function(e,t,n){var r;if(Array.isArray(t))return r=get(n,e),t.every(function(e){return r.includes(e)});throw Error(i18n.__('"allOf" condition requires an array as #3 argument',"notificationx"))},anyOf:function(e,t,n){if(Array.isArray(t))return n=get(n,e),t.includes(n);throw Error(i18n.__('"anyOf" condition requires an array as #3 argument',"notificationx"))},gt:function(e,t,n){return get(n,e)>t},gte:function(e,t,n){return get(n,e)>=t},lt:function(e,t,n){return get(n,e)<t},lte:function(e,t,n){return get(n,e)<=t}},logicalRules={and:function(e){return!e.includes(!1)},or:function(e){return e.includes(!0)},not:function(e){if(1!==e.length)throw Error(i18n.__('"not" can have only one comparison rule, multiple rules given',"notificationx"));return!e[0]}},isValidCondition=function(e){return!!(Array.isArray(e)&&Array.isArray(e[1])&&e[0]&&logicalRules[e[0].toLowerCase()])},processRule=function(e,t){var n=e[0],r=e[1],e=e[2];if("string"!=typeof n||void 0===rules[n])throw Error(i18n.sprintf(i18n.__("Invalid comparison rule %s.","notificationx"),n));return rules[n](r,e,t)},processCondition=function(e,t){return logicalRules[e.toLowerCase()](t)},validate$1=function(e,n){var t,r;return isValidCondition(e)?(t=e.slice(0,1)[0],r=e.slice(1).map(function(e,t){return(isValidCondition(e)?when:processRule)(e,n)}),processCondition(t,r)):processRule(e,n)},when=function(e,t){return"function"==typeof e?Promise.resolve(e(t)):validate$1(e,t)};function ownKeys$f(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$f(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$f(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var DEFAULT_STATE={savedValues:{type:"conversions",source:"edd"},values:{},touched:{},errors:{}},actions={setSavedValues:function(e){return{type:"SET_SAVED_VALUES",payload:e}},setFieldValue:function(e){return{type:"FIELD_VALUE",name:e.name,payload:e.value}},removeFieldValue:function(e){return{type:"REMOVE_FIELD_VALUE",payload:e}},resetFieldValue:function(e){return{type:"RESET_FIELD_VALUE",payload:e}},setFieldTouched:function(e){return{type:"FIELD_TOUCHED",payload:e}},setError:function(e){return{type:"FIELD_ERROR",payload:e}},removeError:function(e){return{type:"REMOVE_FIELD_ERROR",payload:e}}},store={reducer:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:DEFAULT_STATE,t=1<arguments.length?arguments[1]:void 0;switch(t.type){case"SET_SAVED_VALUES":var n=_objectSpread$f({},e);return _objectSpread$f(_objectSpread$f({},n),{},{values:t.payload,savedValues:t.payload});case"FIELD_VALUE":var n=_objectSpread$f({},e),r=t.payload;return _objectSpread$f(_objectSpread$f({},n),{},{values:_objectSpread$f(_objectSpread$f({},null==(n=n)?void 0:n.values),r)});case"REMOVE_FIELD_VALUE":n=_objectSpread$f({},e),r=t.payload;return null!=(a=n.values)&&a[r]&&delete n.values[r],n;case"RESET_FIELD_VALUE":var a=_objectSpread$f({},e);return null!=(r=a.values)&&r[t.payload]&&(delete a.values[t.payload],null!=(n=a.savedValues))&&n[t.payload]&&(a.values[t.payload]=a.savedValues[t.payload]),a;case"FIELD_ERROR":return _objectSpread$f(_objectSpread$f({},e),{},{errors:_objectSpread$f(_objectSpread$f({},e.errors),t.payload)});case"REMOVE_FIELD_ERROR":r=_objectSpread$f({},e);return delete r.errors[t.payload],r;case"FIELD_TOUCHED":return _objectSpread$f(_objectSpread$f({},e),{},{touched:_objectSpread$f(_objectSpread$f({},e.touched),t.payload)})}return e},actions:actions,selectors:{getValues:function(e){return e.values},getFieldValue:function(e,t){return null==(e=e.values)?void 0:e[t]},getSavedFieldValue:function(e,t,n){var r;return(null===n||(null==(r=e.savedValues)?void 0:r[n])===(null==(r=e.values)?void 0:r[n]))&&(null==(r=e.savedValues)?void 0:r[t])},isTouched:function(e,t){return null==(e=e.touched)?void 0:e[t]},getError:function(e,t){return null==(e=e.errors)?void 0:e[t]},isVisible:function(e,t){return!t.rules||null==t.name||(t=when(t.rules,e.values),Boolean(t))}}};function _extends$1(){return(_extends$1=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)!{}.hasOwnProperty.call(r,n)||(e[n]=r[n])}return e}).apply(null,arguments)}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,l,o,i=[],c=!0,u=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=l.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw a}}return i}}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray$2(e,t)||_nonIterableRest()}var addCookiesListItemCount=function(t,e){var n=["necessary_cookie_lists","functional_cookie_lists","analytics_cookie_lists","performance_cookie_lists","advertising_cookie_lists","uncategorized_cookie_lists"].map(function(e){return Array.isArray(null==t?void 0:t.values[e])?null==t||null==(e=t.values[e])?void 0:e.length:0});e.forEach(function(e,t){e.count=n[t]})},Menu=function(r){if(void 0===r.fields)throw new Error(i18n.__("There are no tabs defined!","notificationx"));var a=r.active,l=r.setActive,t=r.fields,o=r.context,e=_slicedToArray(React.useState([]),2),n=e[0],i=e[1],e=(null!=r&&r.dataShare&&addCookiesListItemCount(o,t),React.useEffect(function(){var e=t.filter(function(e){return isVisible(null==o?void 0:o.values,e)});i(e)},[t,null==o||null==(e=o.values)?void 0:e.source]),classNames__default.default("wprf-tab-menu-wrapper",null==r?void 0:r.className,{"wprf-tab-menu-sidebar":null==r?void 0:r.sidebar},null==o||null==(e=o.values)?void 0:e.source)),c=n.findIndex(function(e){return e.id===a});return React.createElement("div",{className:e},React.createElement("ul",{className:"wprf-tab-nav"},n.map(function(t,e){var n;return React.createElement("li",{className:classNames__default.default("wprf-tab-nav-item",_defineProperty(_defineProperty(_defineProperty({},"".concat(t.classes),t.classes),"wprf-active-nav",a===t.id),"wprf-tab-complete",!(null==r||!r.completionTrack)&&e<=c)),"data-key":t.id,key:t.id,onClick:function(){var e;return(null==(e=null==r?void 0:r.clickable)||e)&&l(t.id)}},(null==t?void 0:t.icon)&&(isString(t.icon)&&!isObject(t.icon)?React.createElement("img",{src:t.icon,alt:null==t?void 0:t.label}):isObject(t.icon)?null==o||null==(e=o.icons)||null==(e=e[null==t||null==(n=t.icon)?void 0:n.type])?void 0:e[null==t||null==(n=t.icon)?void 0:n.name]:""),React.createElement("span",null,t.label),(null==r?void 0:r.dataShare)&&React.createElement("span",{className:"list-count"},(null==t?void 0:t.count)<10?"0".concat(null==t?void 0:t.count):null==t?void 0:t.count))})))};function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n,r={};for(n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}function _objectWithoutProperties(e,t){if(null==e)return{};var n,r=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols)for(var a=Object.getOwnPropertySymbols(e),l=0;l<a.length;l++)n=a[l],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n]);return r}var BuilderContext=React.createContext(void 0),BuilderProvider=(BuilderContext.displayName="production"===process.env.NODE_ENV?"Anonymous":"BuilderContext",BuilderContext.Provider),BuilderConsumer=BuilderContext.Consumer;function useBuilderContext(){return React.useContext(BuilderContext)}var useOptions=function(t){var n,r,a,l,e,o,i,c,u,s,d,p,f,m,v=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"fields";if(null!=t&&t[v])return n=t.value,r=t.multiple,a=useBuilderContext(),u=_slicedToArray(React.useState(t[v]),2),l=u[0],e=u[1],u=_slicedToArray(React.useState([]),2),o=u[0],i=u[1],u=_slicedToArray(React.useState({options:null,parentIndex:null}),2),c=u[0],u=u[1],p=_slicedToArray(React.useState(null),2),s=p[0],d=p[1],p=_slicedToArray(React.useState(null),2),f=p[0],m=p[1],React.useEffect(function(){var e=t.ajax&&(null==(e=a.getTabFields(null==t?void 0:t.parentIndex))?void 0:e[v])||l;i(a.eligibleOptions(e)),d(a.eligibleOption(e,n,null!=r&&r))},[n,l]),React.useEffect(function(){e(t[v]),i(a.eligibleOptions(t[v]))},[t]),React.useEffect(function(){i(a.eligibleOptions(l))},[l]),React.useEffect(function(){null!=c.options&&e(c.options)},[c]),React.useEffect(function(){var e;null!=s&&(e=r?isArray(s)&&s.map(function(e){return e.value})||n:s.value||n,m(e))},[s]),React.useEffect(function(){var e;0===o.filter(function(e){return e.value===f}).length&&(e=sortingFields(o),m((null==e||null==(e=e[0])?void 0:e.value)||n))},[f,o]),{options:sortingFields(o),option:f,selectedOption:s,setOptions:i,setData:u};throw new Error("#options param need to set in order to use useOptions hook.")},useTrigger=function(e){var a=e.context;null!=e&&e.trigger&&isArray(null==e?void 0:e.trigger)&&null!=e&&e.trigger.map(function(e){var t=(null==e?void 0:e.type)||"setFieldValue";if(null!=e&&e.action&&isObject(null==e?void 0:e.action))for(var n in null==e?void 0:e.action){var r=n,n=(-1<r.indexOf(".")&&(r=r.split(".")),null==e?void 0:e.action[n]);""!=r&&a[t](r,n)}})},useDefaults=function(e,t,n,r){if(null!=r&&null!=(null==r?void 0:r.defaults)&&!isEmptyObj(r.defaults)){var a=r.defaults;if(null!=a&&!isEmptyObj(a)){var l={};if(null!=a&&a[n]&&isString(null==a?void 0:a[n])){var o,r=a[n].indexOf("@"),i=a[n].indexOf(":");0===r&&0<i&&(r=a[n].substr(1,i-1),i=a[n].substr(i+1),o=t.getValueForDefault(r,e),""!=r)&&""!=i&&(i="false"!==i&&i,l[r]=o||i,t.setValue(r,o||i))}else if(null!=a&&a[n]&&(isArray(a[n])||isObject(a[n])))for(var c in a[n]){var u,s,d=a[n][c];d&&(isArray(d)||isObject(d))?(u=t.getValueForDefault(c,e),""!=c&&""!=d&&(d="false"!==d&&d,l[c]=u||d,t.setValue(c,u||d))):d&&(c=d.indexOf("@"),u=d.indexOf(":"),0===c)&&0<u&&(c=d.substr(1,u-1),s=d.substr(u+1),-1<d.indexOf(".")&&(c=c.split(".")),d=t.getValueForDefault(c,e),""!=c)&&""!=s&&(s="false"!==s&&s,l[c]=d||s,t.setValue(c,d||s))}return{defaultsData:l}}}};function ownKeys$e(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$e(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$e(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$e(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var SweetAlert=function(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return Swal__default.default.fire(_objectSpread$e({target:null!=(e=null==t?void 0:t.target)?e:"#notificationx",type:null!=(e=null==t?void 0:t.type)?e:"success",html:null==t?void 0:t.html,title:null!=(e=null==t?void 0:t.title)?e:i18n.__("Title Goes Here: title","notificationx"),text:null!=(e=null==t?void 0:t.text)?e:i18n.__("Test Goes Here: text","notificationx"),icon:null!=(e=null==t?void 0:t.icon)?e:(null==t?void 0:t.type)||"success",timer:null!=(e=null==t?void 0:t.timer)?e:null},t))},ObjectFilter=function(t,n){var r,e,a=2<arguments.length&&void 0!==arguments[2]&&arguments[2];return!!t&&(r={},e=Object.keys(t).filter(function(e){return n(e)}),a?e:(e.map(function(e){r[e]=t[e]}),r))},isExists=function(e,t){var n=_typeof$1(e);switch(!0){case"object"===n&&isArray(e):return e.includes(t);case"object"===n&&!isArray(e):return void 0!==(null==e?void 0:e[t]);default:return e===t}},triggerDefaults=function(e,t){var n,r,a=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!isEmptyObj(e)&&"object"===_typeof$1(e))for(var l in e)l===a&&(n=e[l].indexOf("@"),r=e[l].indexOf(":"),0===n)&&0<r&&(n=e[l].substr(1,r-1),l=e[l].substr(r+1),r=getStoreData().getSavedFieldValue(n,t),""!=n)&&""!=l&&setStoreData().setFieldValue({name:n,value:_defineProperty({},n,r||l)})},getStoreData=function(){return data.select("formbuilder")},setStoreData=function(){return data.dispatch("formbuilder")},processAjaxData=function(n){var r={};return Object.keys(n).map(function(e){var t;0===n[e].indexOf("@")?""!=(t=n[e].substr(1))&&(t=getStoreData().getFieldValue(t),r[e]=t||"undefined"):r[e]=n[e]}),r};function _extends(){for(var e=arguments.length,a=new Array(e),t=0;t<e;t++)a[t]=arguments[t];return(Object.assign||function(e){for(var t=1;t<a.length;t++){var n,r=a[t];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,a)}function ownKeys$d(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$d(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$d(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var builderReducer=function(e,t){switch(t.type){case"SET_CONTEXT":return _extends({},e,setIn(e,t.payload.field,t.payload.value));case"SET_ACTIVE_TAB":return _objectSpread$d(_objectSpread$d({},e),{},{config:_objectSpread$d(_objectSpread$d({},e.config),{},{active:t.payload})});case"SET_REDIRECT":return _objectSpread$d(_objectSpread$d({},e),{},{redirect:_objectSpread$d(_objectSpread$d({},e.redirect),t.payload)});case"SET_VALUES":return _extends({},e,setIn(e,"values",t.payload));case"SET_SAVED_VALUES":return _extends({},e,setIn(e,"savedValues",t.payload));case"SET_FIELD_VALUE":return _extends({},e,{values:setIn(e.values,t.payload.field,t.payload.value)});case"SET_TOUCHED":return _extends({},e,{touched:t.payload});case"SET_ERRORS":return _extends({},e,{errors:t.payload});case"SET_STATUS":return _extends({},e,{status:t.payload});case"SET_ISSUBMITTING":return _objectSpread$d(_objectSpread$d({},e),{},{isSubmitting:t.payload});case"SET_ISVALIDATING":return _extends({},e,{isValidating:t.payload});case"SET_FIELD_TOUCHED":return _objectSpread$d(_objectSpread$d({},e),{},{touched:_objectSpread$d(_objectSpread$d({},e.touched),{},_defineProperty({},t.payload.field,t.payload.value))});case"SET_FIELD_ERROR":case"RESET_FORM":return _extends({},e,t.payload);case"SUBMIT_ATTEMPT":return _extends({},e,{isSubmitting:!0,submitCount:e.submitCount+1});case"SUBMIT_FAILURE":case"SUBMIT_SUCCESS":return _extends({},e,{isSubmitting:!1});case"SET_FORM_FIELD":return null===t.payload.field?_extends({},e,setIn(e,"tabs",t.payload.value)):_extends({},e,{tabs:setIn(e.tabs,t.payload.field,t.payload.value)});case"SET_ICONS":return _extends({},e,{icons:setIn(e.icons,t.payload.name,t.payload.icons)});case"SET_ALERTS":return _extends({},e,{alerts:setIn(e.alerts,t.payload.name,t.payload.value)});case"SET_COMMONS":return _extends({},e,{common:setIn(e.common,t.payload.name,t.payload.value)});default:return e}};function ownKeys$c(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$c(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$c(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var useBuilder=function(e){var t=React.useRef(!1);React.useEffect(function(){return t.current=!0,function(){t.current=!1}},[]);var n=_slicedToArray(React.useReducer(builderReducer,_objectSpread$c(_objectSpread$c({},e),{},{savedValues:e.savedValues||{},values:e.values||{},errors:e.initialErrors||{},touched:e.initialTouched||{},icons:e.initialIcons||{},common:{},alerts:{},tabs:sortingFields(e.tabs)})),2),c=n[0],r=n[1],n=useEventCallback(function(e,t,n){r({type:"SET_CONTEXT",payload:{field:e,value:t}})}),a=useEventCallback(function(e,t){e="function"==typeof e?e(c.values):e;return r({type:"SET_VALUES",payload:e}),void 0!==t&&t?e:Promise.resolve()}),l=useEventCallback(function(e,t){e="function"==typeof e?e(c.values):e;return r({type:"SET_SAVED_VALUES",payload:e}),void 0!==t&&t?e:Promise.resolve()}),o=useEventCallback(function(e,t,n){r({type:"SET_FIELD_VALUE",payload:{field:e,value:t}})}),i=useEventCallback(function(e,t){r({type:"SET_FORM_FIELD",payload:{field:e,value:t}})}),u=React.useCallback(function(e){return getIn(c.values,e)},[c]),s=useEventCallback(function(e,t,n){r({type:"SET_FIELD_TOUCHED",payload:{field:e,value:t=t||!0}})}),d=React.useCallback(function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],e=(e.persist&&e.persist(),e.target),n=e.name;s(t||n||e.id,!0)},[s]),p=useEventCallback(function(t){if("string"==typeof t)return function(e){return d(e,t)};d(t)}),f=React.useCallback(function(e,t,n){null!=n&&n.isPro&&!1===Boolean(c.is_pro_active)||(e=(n=executeChange(e,t)).field)&&o(e,n.val)},[o,c.values]),m=useEventCallback(function(t,n){var e;if((null!=n&&n.isPro&&!1===Boolean(c.is_pro_active)||null!=n&&null!=(e=n.popup)&&e.forced)&&null!=(e=c.alerts)&&null!=(e=e.pro_alert(null==n?void 0:n.popup))&&e.fire(),null==n||!n.nx_has_permission)return"string"==typeof t?function(e){return f(t,e,n)}:void f(t,null,n);null!=(e=c.alerts)&&null!=(e=e.has_permission_alert(null==n?void 0:n.permission_popup))&&e.fire()}),v=React.useCallback(function(e){var t=_objectSpread$c({},e),n=validFieldProps(t),r=n.name,a=n.type,l=n.parent,o=n.parenttype,i=(null!=t&&t.is_pro&&(n.is_pro=!(null!=t&&t.is_pro&&!0===Boolean(c.is_pro_active))),l=l&&"group"===o?null!=(i=null==(i=null!=(i=getIn(c.values,l))?i:{})?void 0:i[r])?i:null==t?void 0:t.default:l&&"repeater"===o?null!=(i=null==(o=null!=(i=getIn(c.values,l))?i:[])||null==(l=o[n.index])?void 0:l[r])?i:null==t?void 0:t.default:null!=(o=getIn(c.values,r))?o:null==t?void 0:t.default,n.onChange=m,n.onBlur=p,n.value);return"checkbox"!==a||n.multiple?"radio"===a?(n.checked=l===i,n.value=i):n.value="date"===a&&null==l?getTime():l:(n.checked=!!l,n.value=!!l,isString(l)&&"0"===l?(n.checked=!1,n.value=!1):(n.checked=Boolean(l),n.value=Boolean(l))),n.visible=isVisible(c.values,e),n},[p,m,c.values]),b=React.useCallback(function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;return n=null!==n?null==(n=getIn(c.values,n))?void 0:n[e]:getIn(c.values,e)||(null==(n=t.meta)?void 0:n.default),_objectSpread$c(_objectSpread$c({},t.meta),{},{value:n,error:getIn(c.errors,e),touched:!!getIn(c.touched,e),visible:isVisible(c.values,t),initialValue:"",initialTouched:"",initialError:""})},[c.errors,c.touched,c.values]),y=React.useCallback(function(e){return 0<e.length?e.filter(function(e){return null!=e&&e.rules?when(e.rules,c.values):e}):e},[c.errors,c.touched,c.values]),_=React.useCallback(function(e,t){var n;return e.length?(n=[],2<arguments.length&&void 0!==arguments[2]&&arguments[2]&&isArray(t)?e.filter(function(e){return t.includes(e.value)}):0<(n=e.filter(function(e){return e.value==t})).length?n[0]:""):e},[c.errors,c.touched,c.values]),g=React.useCallback(function(){return{setValue:function(e,t){return o(e,t)},getValue:function(e){return getIn(c.values,e)},getValueForDefault:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return(null===t||getIn(c.savedValues,t)===getIn(c.values,t))&&getIn(c.savedValues,e)}}},[c.errors,c.touched,c.values,c.savedValues]),w=React.useCallback(function(e){return getIn(c.tabs,e)},[c]),h=useEventCallback(function(e){r({type:"SET_ISSUBMITTING",payload:e})}),E=useEventCallback(function(e){r({type:"SET_ACTIVE_TAB",payload:e})}),R=useEventCallback(function(e){r({type:"SET_REDIRECT",payload:e})}),O=useEventCallback(function(e,t){r({type:"SET_ICONS",payload:{name:e,icons:t}})}),j=useEventCallback(function(e,t){r({type:"SET_COMMONS",payload:{name:e,value:t}})}),x=useEventCallback(function(e,t){r({type:"SET_ALERTS",payload:{name:e,value:t}})});return _objectSpread$c(_objectSpread$c(_objectSpread$c({},e),c),{},{setContext:n,values:c.values,savedValues:c.savedValues,errors:c.errors,touched:c.touched,isSubmitting:!1,setActiveTab:E,setRedirect:R,setSubmitting:h,setValues:a,setSavedValues:l,setFieldValue:o,getFieldValue:u,handleBlur:p,handleChange:m,getFieldProps:v,getFieldMeta:b,getFieldHelpers:g,eligibleOptions:y,eligibleOption:_,getTabFields:w,setFormField:i,registerIcons:O,registerCommon:j,registerAlert:x})},useIsomorphicLayoutEffect="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?React.useLayoutEffect:React.useEffect,useEventCallback=function(e){var l=React.useRef(e);return useIsomorphicLayoutEffect(function(){l.current=e}),React.useCallback(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var r=arguments.length,t=new Array(r),a=0;a<r;a++)t[a]=arguments[a];return l.current.apply(void 0,t)},[])},Row=function(e){var t=classNames__default.default("wprf-row clearfix wprf-flex",null==e?void 0:e.className);return React.createElement("div",{className:t},null==e?void 0:e.children)},Column=function(e){var t=classNames__default.default("wprf-column",null==e?void 0:e.className,_defineProperty(_defineProperty({},"wprf-column-".concat(12/(null==e?void 0:e.column)),(null==e?void 0:e.column)&&12!==e.column),"wprf-column-12",12===e.column));return React.createElement("div",{className:t},null==e?void 0:e.children)},Label=function(e){var t=classNames__default.default("wprf-input-label",null==e?void 0:e.className);return React.createElement("label",{htmlFor:null==e?void 0:e.htmlFor,className:t},(null==e||null==(t=e.badge)?void 0:t.value)&&React.createElement("div",{className:"wprf-badge"},React.createElement("sup",{className:classNames__default.default("wprf-badge-item",{"wprf-badge-active":null==e||null==(t=e.badge)?void 0:t.active})},null==e||null==(t=e.badge)?void 0:t.label)),!(null!=e&&e.src)&&(null==e?void 0:e.children),(null==e?void 0:e.src)&&React.createElement(Image,{className:"wprf-label-image",src:e.src,alt:null==e?void 0:e.label}))},Image=function(e){var t;return null!=e&&e.src?(t=classNames__default.default(["wprf-input-image",null==e?void 0:e.className]),React.createElement("img",{className:t,src:null==e?void 0:e.src,alt:null==e?void 0:e.alt})):React.createElement("p",null,"No Source( src ) Defined")},BadgeComp=function(e){var t=e.componentClasses;return React.createElement("div",{className:"wprf-badge"},React.createElement("sup",{className:t},e.label))},Badge=function(t){var n=useBuilderContext(),e=t.label,r=t.position,r=void 0===r?"right":r,a=t.renderLabel,l=t.renderComponent,o=(void 0===e&&(e="Pro"),classNames__default.default("wprf-badge-item",{"wprf-badge-active":t.active})),i={};return n.is_pro_active||(i={onClick:function(e){e.preventDefault(),n.alerts.pro_alert(null==t?void 0:t.popup).fire()}}),React.createElement("div",_extends$1({className:classNames__default.default("wprf-badge-wrapper",{"pro-deactivated":!n.is_pro_active})},i),"left"===r&&0<e.length&&React.createElement(React.Fragment,null,a(React.createElement(BadgeComp,{componentClasses:o,label:e}),"left")),"right"===r&&0<e.length&&React.createElement(React.Fragment,null,a(React.createElement(BadgeComp,{componentClasses:o,label:e}),"right")),l())},_excluded$4=["id","label","badge","badgePosition","info","context","tooltip"],ControlLabel=function(e){var t=e.id,n=e.label,r=e.badge,a=e.badgePosition,l=e.info,o=e.context,i=e.tooltip,e=_objectWithoutProperties(e,_excluded$4);return n&&0<n.length?React.createElement("div",{className:"wprf-control-label"},"left"==a&&r,React.createElement("label",{htmlFor:t},n),i&&React.createElement("div",{className:"wprf-control-label-tooltip"},React.createElement("img",{src:null==i?void 0:i.icon}),React.createElement("div",{dangerouslySetInnerHTML:{__html:null==i?void 0:i.content}})),l&&React.createElement("div",{className:"wprf-info"},React.createElement("button",{className:"wprf-info-button"},"Info"),React.createElement("p",{className:"wprf-info-text"},React.createElement("span",{dangerouslySetInnerHTML:{__html:l}}))),(null==e?void 0:e.link)&&React.createElement("a",{rel:"nofollow",target:"_blank",href:e.link},null==o||null==(t=o.icons)?void 0:t.link),"right"==a&&r):null},ControlField=function(e){var t=e.position,n=e.description,r=e.renderComponent,e=e.help;return React.createElement("div",{className:"wprf-control-field"},"left"===t&&n&&React.createElement("p",{className:"wprf-description",dangerouslySetInnerHTML:{__html:n}}),r(),"right"===t&&n&&React.createElement("p",{className:"wprf-description",dangerouslySetInnerHTML:{__html:n}}),e&&React.createElement("p",{className:"wprf-help",dangerouslySetInnerHTML:{__html:e}}))},_excluded$3=["label","id","name","type","style","is_pro","badge"];function ownKeys$b(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$b(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$b(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$b(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var withLabel=function(d){return function(t){var n,r=t.label,a=t.id,e=t.name,l=t.type,o=t.style,i=t.is_pro,c=t.badge,u=_objectWithoutProperties(t,_excluded$3),s=(null==a&&(a=e),_objectSpread$b({description:{position:"right"}},o)),o=classNames__default.default(_defineProperty(_defineProperty(_defineProperty({},"wprf-style-".concat(null==s?void 0:s.type),(null==s?void 0:s.type)||!1),"wprf-label-none",void 0===r||""===r||0===r.length),"wprf-".concat((null==s||null==(o=s.label)?void 0:o.position)||"inline","-label"),(null==(o=null==s||null==(o=s.label)?void 0:o.position)||o)&&null!=r));return"hidden"===l?React.createElement(d,_extends$1({},t,{id:a})):(n=validFieldProps(t,["description","label","help","style"]),l=classNames__default.default("wprf-control-wrapper","wprf-type-".concat(l),o,null==t?void 0:t.classes,_defineProperty({},"wprf-name-".concat(e),e)),React.createElement("div",{className:l},1==i&&React.createElement(React.Fragment,null,React.createElement(Badge,_extends$1({},c,u,{renderLabel:function(e,t){return React.createElement(ControlLabel,_extends$1({},n,{context:null==u?void 0:u.context,id:a,label:r,badge:e,badgePosition:t}))},renderComponent:function(){var e;return React.createElement(ControlField,{help:null,description:null==t?void 0:t.description,position:null==s||null==(e=s.description)?void 0:e.position,renderComponent:function(){return React.createElement(d,_extends$1({},n,{disable:!0,id:a}))}})}})),(null==t?void 0:t.help)&&React.createElement("div",{className:"wprf-badge-wrapper"},React.createElement("div",{className:"wprf-control-label"}),React.createElement("div",{className:"wprf-control-field"},React.createElement("p",{className:"wprf-help",dangerouslySetInnerHTML:{__html:t.help}})))),(0==i||null==i)&&React.createElement(React.Fragment,null,r&&0<r.length&&React.createElement(ControlLabel,_extends$1({},n,{context:null==u?void 0:u.context,label:r,id:a})),React.createElement(ControlField,{help:null==t?void 0:t.help,description:null==t?void 0:t.description,position:null==s||null==(o=s.description)?void 0:o.position,renderComponent:function(){return React.createElement(d,_extends$1({},n,{id:a}))}}))))}},withProps=function(c){var u=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return function(n){var e=useBuilderContext(),t=n.trigger,r=e.getFieldProps(n),a=e.getFieldMeta(r.name,n),l=e.getFieldHelpers(),o=(null!=e&&e.quickBuilder&&null!=e&&e.show&&(e.show.includes(n.name)||(r.classes=null!=r&&r.classes?r.classes+" hidden":" hidden")),null!=n&&n.parentIndex?_toConsumableArray(n.parentIndex):[]),i=(r.parentIndex=o,r.context=e,isFunction(n.onChange)&&(r.onChange=n.onChange),isFunction(n.onBlur)&&(r.onBlur=n.onBlur),React.useRef({}));return React.useEffect(function(){return i.current[n.name]=!0,function(){i.current[n.name]=!1}},[]),React.useEffect(function(){var e,t;["ft_theme_three_line_one","ft_theme_three_line_two","ft_theme_four_line_two"].includes(n.name)||a.visible&&i.current[n.name]&&(u||"group"===r.type?(e=null==n?void 0:n.parent,t=null==n?void 0:n.parenttype,e&&"group"===t&&r.value&&l.setValue([e,r.name],r.value)):l.setValue(r.name,r.value))},[a.visible]),React.useEffect(function(){i.current[n.name]&&isObject(t)&&!isEmptyObj(t)&&useDefaults(r.name,l,r.value,t)},[r.value,a.visible]),a.visible?React.createElement(c,r):React.createElement(React.Fragment,null)}};function _createForOfIteratorHelper$1(e,t){var n,r,a,l,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return a=!(r=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return r=e.done,e},e:function(e){a=!0,n=e},f:function(){try{r||null==o.return||o.return()}finally{if(a)throw n}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray$1(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),l=0,{s:t=function(){},n:function(){return l>=e.length?{done:!0}:{done:!1,value:e[l++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$1(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$1(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$1(e,t):void 0}function _arrayLikeToArray$1(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ownKeys$a(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$a(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$a(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function GenericCheckbox(t){var e=_objectSpread$a({type:"",label:{position:"right"},column:4},t.style),n=React.useMemo(function(){var e=!1;return null!=t&&t.checked&&isObject(t.checked)&&isString(null==t?void 0:t.value)?e=t.checked[t.value]:isString(t.value)||(e=t.value),e},[null==t?void 0:t.checked,t.value]),e=classNames__default.default("wprf-checkbox-wrap",_defineProperty(_defineProperty(_defineProperty({},"wprf-".concat(null==e?void 0:e.type),0<(null==e?void 0:e.type.length)),"wprf-checked",Boolean(n)),"wprf-label-position-".concat(null==e||null==(n=e.label)?void 0:n.position),null==e||null==(n=e.label)?void 0:n.position),null==t?void 0:t.classes);return React.createElement("div",{className:e},React.createElement(GenericInput,_objectSpread$a(_objectSpread$a({},t),{},{type:"checkbox"})),React.createElement("label",{htmlFor:t.id},t.label))}function Checkbox(t){var n,r,a,e=t.options,l=t.value,o=t.multiple,i=t.style,c=sortingFields(e),u=_objectSpread$a({column:4},i);return o?(e=_slicedToArray(React.useState({}),2),n=e[0],r=e[1],a=function(e){var t=e.target||e.currentTarget;r(function(e){return _objectSpread$a(_objectSpread$a({},e),{},_defineProperty({},t.value,t.checked))})},React.useEffect(function(){t.onChange({target:{type:"checkbox",name:t.name,value:n,multiple:!0}})},[n]),React.useEffect(function(){if(isObject(l))r(l);else{var e,t={},n=_createForOfIteratorHelper$1(c);try{for(n.s();!(e=n.n()).done;)t[e.value.value]=l}catch(e){n.e(e)}finally{n.f()}r(t)}},[]),React.createElement("div",{className:"wprf-checkbox-wrapper wprf-control"},React.createElement(Row,null,c.map(function(e){return React.createElement(Column,{key:e.value,column:u.column},React.createElement(GenericCheckbox,_objectSpread$a(_objectSpread$a({},e),{},{context:null==t?void 0:t.context,id:e.value,checked:void 0===n[e.value]||(null!=n&&n[e.value]?l:!(null==n||!n[e.value])),type:"checkbox",onChange:a,style:u})))})))):React.createElement(GenericInput,_objectSpread$a(_objectSpread$a({},t),{},{type:"checkbox"}))}var Checkbox$1=withLabel(Checkbox),Field=function(e){if(!e.type||0===e.type.length)throw console.error(e),new Error(i18n.__("Field must have a #type. see documentation.","notificationx"));switch(e.type){case"text":case"radio":case"email":case"range":case"number":case"hidden":return React.createElement(Input$1,e);case"checkbox":return React.createElement(Checkbox$1,e);case"textarea":return React.createElement(Textarea$1,e);case"codeviewer":return React.createElement(CodeViewer$1,e);case"message":return React.createElement(Message,e);case"select":return React.createElement(Select$1,e);case"select-async":return React.createElement(SelectAsync$1,e);case"slider":return React.createElement(Slider,e);case"group":return React.createElement(Group$1,e);case"radio-card":return React.createElement(Radio,e);case"section":return React.createElement(Section$1,e);case"date":return React.createElement(Date$1,e);case"toggle":return React.createElement(Toggle,e);case"colorpicker":return React.createElement(ColorPicker$1,e);case"jsonuploader":return React.createElement(JsonUploader$1,e);case"repeater":return React.createElement(Repeater,e);case"media":return React.createElement(Media$1,e);case"editor":return React.createElement(Editor$1,e);case"action":return React.createElement(Action,e);case"button":return React.createElement(Button$1,e);case"modal":return React.createElement(Modal,e);case"tab":return React.createElement(Tab,e);case"responsive-number":return React.createElement(ResponsiveNumber$1,e);default:var t=hooks.applyFilters("custom_field","",e.type,e);return React.createElement(React.Fragment,null,t)}},GenericField=withProps(Field,!0),Field$1=withProps(Field),DateControl=function(e){var t=e.name,n=e.value,r=e.onChange,a=e.position,l=date.__experimentalGetSettings(),o=null!=(e=null==e?void 0:e.format)?e:l.formats.datetime,i=getTime(n),c=/a(?!\\)/i.test(l.formats.datetime.toLowerCase().replace(/\\\\/g,"").split("").reverse().join(""));return React.useEffect(function(){r({target:{type:"date",name:t,value:i}})},[]),React.createElement(components.Dropdown,{className:"wprf-control-datetime",contentClassName:"wprf-control-datetime-content",position:a||"bottom right",renderToggle:function(e){return e.isOpen,React.createElement(components.Button,{isTertiary:!0,onClick:e.onToggle},date.date(o,i,-(new Date).getTimezoneOffset()))},renderContent:function(){return React.createElement(components.DateTimePicker,{__nextRemoveHelpButton:!0,__nextRemoveResetButton:!0,currentDate:getTime(i).toDate().toString(),onChange:function(e){r({target:{type:"date",name:t,value:moment__default.default(e).utc().format()}})},is12Hour:c})}})},Date$1=withLabel(DateControl);function ownKeys$9(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$9(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$9(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$9(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Input=function(t,e){function n(e){return a.onChange(e,{popup:null==t?void 0:t.popup,isPro:!!t.is_pro,nx_has_permission:!!t.nx_has_permission,permission_popup:null==t?void 0:t.nx_has_permission})}var r=t.type||"text",a=validFieldProps(_objectSpread$9(_objectSpread$9({},t),{},{type:r}),["is_pro","nx_has_permission","visible","trigger","copyOnClick","disable","parentIndex","context","badge","popup","tags"]),r=React.useRef(null),e=null!=e&&e.current?e:r,l=useBuilderContext();"checkbox"===a.type&&null!=a&&a.name&&(a.checked=(null==a?void 0:a.checked)||(null==a?void 0:a.value));var o,r=_slicedToArray(React.useState(!1),2),i=r[0],c=r[1],u=(React.useEffect(function(){var e;return i&&(e=setTimeout(function(){c(!1)},2e3)),function(){return e&&clearTimeout(e)}},[i]),React.useCallback(function(e){e=null==e||null==(e=e.target)?void 0:e.getAttribute("data-num-sug");l.setFieldValue(a.name,e)},[a]));return!t.is_pro&&null!=t&&t.copyOnClick&&null!=t&&t.value?(r=(null==t?void 0:t.copyMessage)||"Click To Copy!",o=(null==t?void 0:t.copiedMessage)||"Copied!",React.createElement("span",{className:"wprf-clipboard-wrapper"},React__default.default.createElement("input",_objectSpread$9(_objectSpread$9({},a),{},{onChange:n})),React.createElement("span",{className:"wprf-clipboard-tooltip"},React.createElement("span",{className:"wprf-clipboard-tooltip-text"},i?o:r),React.createElement(components.Button,{className:"wprf-copy-icon",onClick:function(){copy__default.default(t.value,{format:"text/plain",onCopy:function(){c(!0)}})}},"Copy")))):React.createElement("span",null,React__default.default.createElement("input",_objectSpread$9(_objectSpread$9({},a),{},{onChange:n,ref:e})),(null==a?void 0:a.suggestions)&&0<(null==a?void 0:a.suggestions.length)&&React.createElement("div",{className:"wprf-num-suggestions"},null==a||null==(o=a.suggestions)?void 0:o.map(function(e,t){return React.createElement("span",{onClick:u,"data-num-sug":e.value},e.value+" "+e.unit)})))},GenericInput=React__default.default.memo(React__default.default.forwardRef(Input)),Input$1=withLabel(React__default.default.memo(Input));function ownKeys$8(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$8(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$8(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$8(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Textarea=function(t){var n=validFieldProps(t,["is_pro","visible","trigger","disable","parentIndex","context"]),e=React.useCallback(function(e){return n.onChange(e,{isPro:!!t.is_pro})},[null==n?void 0:n.value]);return React__default.default.createElement("textarea",_objectSpread$8(_objectSpread$8({},n),{},{onChange:e,rows:5}))},Textarea$1=withLabel(React__default.default.memo(Textarea));function ownKeys$7(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$7(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$7(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$7(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var CodeViewer=function(t){var n=validFieldProps(t,["is_pro","visible","trigger","disable","parentIndex","context","copyOnClick"]),e={onChange:React.useCallback(function(e){return n.onChange(e,{isPro:!!t.is_pro})},[null==n?void 0:n.value]),rows:5},r=(!t.is_pro&&null!=t&&t.copyOnClick&&null!=t&&t.value&&(e.onClick=function(){var e=null!=t&&t.success_text?t.success_text:i18n.__("Copied to Clipboard.","notificationx");copy__default.default(t.value,{format:"text/plain",onCopy:function(){t.context.alerts.toast("success",e)}})}),null!=t&&t.button_text?t.button_text:i18n.__("Click to Copy","notificationx"));return React.createElement("span",{className:"wprf-code-viewer"},React__default.default.createElement("textarea",_objectSpread$7(_objectSpread$7({},n),e)),React.createElement(components.Button,{className:"wprf-copy-button"},r))},CodeViewer$1=withLabel(React__default.default.memo(CodeViewer)),JsonUploader=function(n){validFieldProps(n,["is_pro","visible","trigger","disable","parentIndex","context","copyOnClick"]);var e=_slicedToArray(React.useState(),2),t=e[0],r=e[1];return React.useEffect(function(){null!=n&&n.value||r(null)},[null==n?void 0:n.value]),React.createElement("span",{className:"wprf-json-uploader"},!t&&React.createElement("label",{className:"wprf-json-uploaderButton"},React.createElement("span",null,i18n.__("Upload")),React.createElement("input",{type:"file",accept:"application/JSON",onChange:function(e){var t;(e=e).target.files.length&&(0==(null==(e=e.target.files[0])?void 0:e.size)?n.context.alerts.toast("error",i18n.__("File can't be empty.","notificationx")):"application/json"!=(null==e?void 0:e.type)&&"text/json"!=(null==e?void 0:e.type)?n.context.alerts.toast("error",i18n.__("Invalid file type.","notificationx")):(r(e),(t=new FileReader).onload=function(e){e=null==e||null==(e=e.target)?void 0:e.result;n.onChange({target:{type:"jsonuploader",name:n.name,value:e}})},t.readAsText(e)))}})),t&&(null==t?void 0:t.name)&&React.createElement("span",{className:"wpfr-json-file-name-wrapper"},React.createElement("span",{className:"wpfr-json-file-name"},20<(null==t?void 0:t.name.length)?"".concat(null==t?void 0:t.name.substr(0,9),"...").concat(null==t?void 0:t.name.substr((null==t?void 0:t.name.length)-7)):null==t?void 0:t.name),React.createElement("span",{className:"wprf-json-file-delete-button",onClick:function(){r(null),n.onChange({target:{type:"jsonuploader",name:n.name,value:null}})}},"x")))},JsonUploader$1=withLabel(React__default.default.memo(JsonUploader)),_excluded$2=["name","fields"],Group=function(n){var r,a,e,t,l=n.name,o=n.fields,i=_objectWithoutProperties(n,_excluded$2);if(o&&isArray(o)&&0!==o.length)return r=useBuilderContext(),a=React.useCallback(function(e){e.persist&&e.persist();var e=executeChange(e),t=e.field;r.setFieldValue([l,t],e.val)},[n.value]),e=sortingFields(o),React.useEffect(function(){r.setFormField([].concat(_toConsumableArray(n.parentIndex),["fields"]),e)},[]),o=e.map(function(e,t){t=[].concat(_toConsumableArray(n.parentIndex),["fields",t]);return React.createElement(GenericField,_extends$1({},i,{key:e.name,index:n.index,onChange:a},e,{parenttype:"group",parent:l,parentIndex:t}))}),t=classNames__default.default("wprf-group-control-inner",{"wprf-display-inline":"inline"===(null==n?void 0:n.display)}),React.createElement("div",{className:"wprf-group-control"},React.createElement("div",{className:t},o));throw new Error(i18n.__("You should give a #fields arguments to a group field.","notificationx"))},Group$1=withLabel(Group);function ownKeys$6(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$6(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$6(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$6(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Select=function(a){function e(){if(a.ajax&&(!a.ajax.rules||when(a.ajax.rules,l.values))){y(!0);var r={};if(Object.keys(null==a?void 0:a.ajax.data).map(function(e){var t,n;-1<(null==a?void 0:a.ajax.data[e].indexOf("@"))?(n=null==a?void 0:a.ajax.data[e].substr(1),r[e]=null==(t=l.values)?void 0:t[n]):r[e]=null==a?void 0:a.ajax.data[e]}),!_)return wpFetch({path:null==a?void 0:a.ajax.api,data:r}).then(function(e){y(!1);var t=merge(a.options,e,"value");return l.setFormField([].concat(_toConsumableArray(u),["options"]),t),f({options:t,parentIndex:[].concat(_toConsumableArray(u),["options"])}),e})}}var l=useBuilderContext(),t=a.id,n=a.name,r=a.multiple,o=a.placeholder,i=a.search,i=void 0!==i&&i,c=a.onChange,u=a.parentIndex,s=useOptions(a,"options"),d=s.options,p=s.selectedOption,f=s.setData,s=_slicedToArray(React.useState(null),2),m=s[0],v=s[1],s=_slicedToArray(React.useState(!1),2),b=s[0],y=s[1],s=_slicedToArray(React.useState(!1),2),_=s[0];return React.useEffect(function(){!isArray(m)&&isObject(m)&&c({target:{type:"select",name:n,value:m.value,options:d,multiple:r}}),isArray(m)&&c({target:{type:"select",name:n,value:m.map(function(e){return e.value}),options:d,multiple:r}})},[m]),React.useEffect(function(){e()},[]),React.useEffect(function(){null!=a&&a.menuOpen&&e()},[null==a?void 0:a.menuOpen]),React.createElement("div",{className:"wprf-select-wrapper"},React.createElement(ReactSelect__default.default,{isDisabled:null==a?void 0:a.disable,classNamePrefix:"wprf-select",isSearchable:null!=i&&i,id:t,name:n,isMulti:null!=r&&r,placeholder:o,isLoading:b,options:d,value:p,onMenuOpen:e,onMenuClose:function(){y(!1)},isOptionDisabled:function(e){return null==e?void 0:e.disabled},onChange:function(e){!isArray(e)&&isObject(e)?v(_objectSpread$6({},e)):isArray(e)?v(_toConsumableArray(e)):v(e)}}))},Select$1=withLabel(Select);let instanceMap=new WeakMap;function createId(e){var t=instanceMap.get(e)||0;return instanceMap.set(e,t+1),t}function useInstanceId(t,n,r){return React.useMemo(()=>{var e;return r||(e=createId(t),n?n+"-"+e:e)},[t])}var _RepeaterField=function(t){var e=useBuilderContext(),n=t.fields,r=t.onChange,a=t.index,l=t.parent,o=_slicedToArray(React.useState(t.isCollapsed),2),i=o[0],c=o[1],u=useInstanceId(_RepeaterField),o=null==(o=e.values)||null==(o=o[l])?void 0:o[a],o=(null==o?void 0:o.title)||(null==o?void 0:o.post_title)||(null==o?void 0:o.username)||(null==o?void 0:o.plugin_theme_name),o=o?o.length<40?o:o.substr(0,40)+"...":"";return React.useEffect(function(){e.setFieldValue([l,a,"isCollapsed"],i)},[i]),React.createElement("div",{className:"wprf-repeater-field"},React.createElement("div",{className:"wprf-repeater-field-title",onClick:function(){return c(!i)}},React.createElement("h4",null,React.createElement(components.Icon,{icon:"move"}),t.index+1,": ",o),React.createElement("div",{className:"wprf-repeater-field-controls"},React.createElement(components.Icon,{onClick:function(e){null!=e&&e.stopPropagation(),t.clone(t.index)},icon:"admin-page"}),React.createElement(components.Icon,{onClick:function(e){null!=e&&e.stopPropagation(),t.remove(t.index)},icon:"trash"}))),!i&&React.createElement("div",{className:"wprf-repeater-inner-field"},n.map(function(e,t){return React.createElement(GenericField,_extends$1({key:"field-".concat(a,"-").concat(t)},e,{id:"field-".concat(u,"-").concat(a,"-").concat(t),index:a,parenttype:"repeater",parent:l,onChange:function(e){return r(e,a)}}))})))};function ownKeys$5(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$5(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$5(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$5(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var GenericToggle=function(t){var e=_objectSpread$5({type:"",label:{position:"right"},column:4},t.style),n=React.useMemo(function(){var e=!1;return null!=t&&t.checked&&isObject(t.checked)&&isString(null==t?void 0:t.value)?e=t.checked[t.value]:isString(t.value)||(e=t.value),e},[null==t?void 0:t.checked,t.value]),e=classNames__default.default("wprf-toggle-wrap",_defineProperty(_defineProperty(_defineProperty({},"wprf-".concat(null==e?void 0:e.type),0<(null==e?void 0:e.type.length)),"wprf-checked",Boolean(n)),"wprf-label-position-".concat(null==e||null==(n=e.label)?void 0:n.position),null==e||null==(n=e.label)?void 0:n.position),null==t?void 0:t.classes);return React.createElement("div",{className:e},React.createElement(GenericInput,_objectSpread$5(_objectSpread$5({},t),{},{type:"checkbox",placeholder:void 0})),React.createElement(Label,{htmlFor:t.id}))},GenericToggle$1=withLabel(GenericToggle),ModalContent=function(n){var e=n.isLoading,t=n.closeModal,r=_slicedToArray(React.useState([]),2),a=r[0],l=r[1];return React.useEffect(function(){var e=sortingFields(n.body.fields).map(function(e,t){t=[].concat(_toConsumableArray(n.parentIndex),["fields",t]);return React.createElement(Field$1,_extends$1({key:e.name},e,{parentIndex:t}))});l(e)},[]),React.createElement("div",{className:"wprf-modal-body"},e&&React.createElement(Loading,null),!e&&React.createElement(React.Fragment,null,React.createElement("div",{className:"wprf-modal-content"},0<a.length&&a),React.createElement("div",{className:"wprf-modal-footer clearfix"},React.createElement("div",{className:"wprf-modal-footer-left"},(null==(r=n.body)?void 0:r.footer)&&isString(n.body.footer)&&React.createElement("p",null,n.body.footer),null==n||!n.confirm_button||null!=n&&null!=(e=n.confirm_button)&&e.close_action?"":React.createElement(GenericField,_extends$1({type:"button"},n.confirm_button)),null!=n&&n.confirm_button&&null!=n&&null!=(a=n.confirm_button)&&a.close_action?React.createElement(GenericField,{type:"button",onClick:t,text:null==n||null==(r=n.confirm_button)?void 0:r.text}):""))))},ModalHeader=function(e){e=e.content;return React.createElement("div",{className:"wprf-modal-header"},e&&isString(e)&&React.createElement("h3",null,e))},Loading=function(e){return React.createElement("p",null,i18n.__("Loading...","notificationx"))},toolbarOptions={options:["inline","blockType","textAlign","colorPicker","link"],inline:{options:["bold","italic","underline","strikethrough","monospace"]},blockType:{inDropdown:!0,options:["Normal","H1","H2","H3","H4","H5","H6","Blockquote","Code"],className:void 0,component:void 0,dropdownClassName:void 0}};function _createForOfIteratorHelper(e,t){var n,r,a,l,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return a=!(r=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return r=e.done,e},e:function(e){a=!0,n=e},f:function(){try{r||null==o.return||o.return()}finally{if(a)throw n}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),l=0,{s:t=function(){},n:function(){return l>=e.length?{done:!0}:{done:!1,value:e[l++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ownKeys$4(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$4(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$4(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$4(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Toggle=function(t){var n,r,a,e=t.options,l=t.value,o=t.multiple,i=t.style,c=sortingFields(e),u=_objectSpread$4({column:4},i);return o?(e=_slicedToArray(React.useState({}),2),n=e[0],r=e[1],a=function(e){var t=e.target||e.currentTarget;r(function(e){return _objectSpread$4(_objectSpread$4({},e),{},_defineProperty({},t.value,t.checked))})},React.useEffect(function(){t.onChange({target:{type:"toggle",name:t.name,value:n}})},[n]),React.useEffect(function(){if(isObject(l))r(l);else{var e,t={},n=_createForOfIteratorHelper(c);try{for(n.s();!(e=n.n()).done;)t[e.value.value]=l}catch(e){n.e(e)}finally{n.f()}r(t)}},[]),React.createElement("div",{className:"wprf-toggle-wrapper wprf-control"},React.createElement(Row,null,c.map(function(e){return React.createElement(Column,{key:e.value,column:u.column},React.createElement(GenericToggle$1,_objectSpread$4(_objectSpread$4({},e),{},{context:null==t?void 0:t.context,id:e.value,checked:void 0===n[e.value]||(null!=n&&n[e.value]?l:!(null==n||!n[e.value])),type:"checkbox",onChange:a,style:u})))})))):React.createElement(GenericToggle$1,t)},_excluded$1=["label","value","icon","is_pro"];function ownKeys$3(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$3(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$3(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$3(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var getRandomValues,_RadioCard=function(e){var i,c,u,s=useBuilderContext(),t=useOptions(e,"options"),n=t.options,d=t.option;if(n)return i=useInstanceId(_RadioCard),t=classNames__default.default(["wprf-control","wprf-radio-card","wprf-input-radio-set-wrap",null==e?void 0:e.className]),c=_objectSpread$3({},null==e?void 0:e.style),u=validFieldProps(e,["options","placeholder","style","trigger"]),React.useEffect(function(){d&&e.onChange({target:{type:"radio-card",name:e.name,value:d}})},[d]),React.createElement("div",{className:t},React.createElement(Row,null,n.map(function(e,t){var n,r=e.label,a=e.value,l=e.icon,o=e.is_pro,e=_objectWithoutProperties(e,_excluded$1);return React.createElement(Column,{column:+(null==e?void 0:e.column)||4,key:t},React.createElement("div",{className:classNames__default.default("wprf-input-radio-option",{"wprf-option-has-image":null!=l&&l,"wprf-option-selected":a==d})},React.createElement(Label,{className:classNames__default.default(_defineProperty({"wprf-label-has-image":null!=l&&l},"wprf-size-".concat(c.size),null!=(n=l&&(null==c?void 0:c.size))&&n)),htmlFor:"wprf-input-radio-".concat(i,"-").concat(t),src:l,badge:{label:o?"Pro":"Free",value:o,active:Boolean(s.is_pro_active)}},r),React.createElement(GenericInput,_extends$1({},e,u,{is_pro:o,type:"radio",value:a,checked:a===d,id:"wprf-input-radio-".concat(i,"-").concat(t)}))))})));throw new Error(i18n.__("#options is a required arguments for RadioCard field.","notificationx"))},Radio=withLabel(_RadioCard),Section=function(n){var t=useBuilderContext(),e=_slicedToArray(React.useState(null!=(e=n.collapsed)&&e),2),r=e[0],a=e[1],e=_slicedToArray(React.useState([]),2),l=e[0],o=e[1],e=(React.useEffect(function(){var e=sortingFields(n.fields),e=(t.setFormField([].concat(_toConsumableArray(n.parentIndex),["fields"]),e),e.map(function(e,t){t=[].concat(_toConsumableArray(n.parentIndex),["fields",t]);return React.createElement(Field$1,_extends$1({key:e.name},e,{parentIndex:t}))}));o(e)},[]),classNames__default.default("wprf-control-section",null==n?void 0:n.classes,null==n?void 0:n.name,{"wprf-section-collapsed":(null==n?void 0:n.collapsible)&&r}));return React.createElement("div",{id:null==n?void 0:n.name,className:e},n.placeholder&&React.createElement("div",{className:"wprf-section-title"},React.createElement("h4",null,n.placeholder),n.collapsible&&React.createElement("button",{onClick:function(){return a(!r)}},"Icon")),React.createElement("div",{className:"wprf-section-fields"},l))},Section$1=React__default.default.memo(Section),rnds8=new Uint8Array(16);function rng(){if(getRandomValues=getRandomValues||"undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto))return getRandomValues(rnds8);throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported")}var REGEX=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function validate(e){return"string"==typeof e&&REGEX.test(e)}for(var byteToHex=[],i=0;i<256;++i)byteToHex.push((i+256).toString(16).substr(1));function stringify(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,e=(byteToHex[e[t+0]]+byteToHex[e[t+1]]+byteToHex[e[t+2]]+byteToHex[e[t+3]]+"-"+byteToHex[e[t+4]]+byteToHex[e[t+5]]+"-"+byteToHex[e[t+6]]+byteToHex[e[t+7]]+"-"+byteToHex[e[t+8]]+byteToHex[e[t+9]]+"-"+byteToHex[e[t+10]]+byteToHex[e[t+11]]+byteToHex[e[t+12]]+byteToHex[e[t+13]]+byteToHex[e[t+14]]+byteToHex[e[t+15]]).toLowerCase();if(validate(e))return e;throw TypeError("Stringified UUID is invalid")}function v4(e,t,n){var r=(e=e||{}).random||(e.rng||rng)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var a=0;a<16;++a)t[n+a]=r[a];return t}return stringify(r)}function ownKeys$2(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$2(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$2(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$2(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Repeater=function(e){var a=e.name,t=e.button,n=e.fields,l=useBuilderContext(),e=_slicedToArray(React.useState(null==(e=l.values)?void 0:e[a]),2),r=e[0],o=e[1],i=(React.useEffect(function(){var e;null!=(null==(e=l.values)?void 0:e[a])&&o(null==(e=l.values)?void 0:e[a])},[null==(e=l.values)?void 0:e[a]]),React.useCallback(function(e){var t=_toConsumableArray(r);t.splice(e,1),l.setFieldValue(a,t)},[r])),c=React.useCallback(function(e){var t=_toConsumableArray(r);0<t.length&&(null!=(e=t=null!=(e=t=null!=(e=t=null!=(e=t=(null==t?void 0:t[e])||{})&&e.title?_objectSpread$2(_objectSpread$2({},t),{},{title:t.title+" - Copy"}):t)&&e.post_title?_objectSpread$2(_objectSpread$2({},t),{},{post_title:t.post_title+" - Copy"}):t)&&e.username?_objectSpread$2(_objectSpread$2({},t),{},{username:t.username+" - Copy"}):t)&&e.plugin_theme_name&&(t=_objectSpread$2(_objectSpread$2({},t),{},{plugin_theme_name:t.plugin_theme_name+" - Copy"})),t=_objectSpread$2(_objectSpread$2({},t),{},{index:v4(),isCollapsed:!1}),l.setFieldValue([a,r.length],t))},[r]);return React.useEffect(function(){o(null==r||""==r?[{index:v4()}]:function(e){return e.map(function(e){return _objectSpread$2(_objectSpread$2({},e),{},{index:v4()})})})},[]),React.createElement("div",{className:"wprf-repeater-control"},r&&0<(null==r?void 0:r.length)&&React.createElement(reactSortablejs.ReactSortable,{className:"wprf-repeater-content",list:r,setList:function(e){l.setFieldValue(a,e)},handle:".wprf-repeater-field-title",filter:".wprf-repeater-field-controls",forceFallback:!0},r.map(function(e,r){return React.createElement(_RepeaterField,{isCollapsed:null==e?void 0:e.isCollapsed,key:(null==e?void 0:e.index)||r,fields:n,index:r,parent:a,clone:c,remove:i,onChange:function(e){var t,n;t=r,(e=e).persist&&e.persist(),e=executeChange(e),n=e.field,l.setFieldValue([a,t,n],e.val)}})})),React.createElement("div",{className:"wprf-repeater-label"},React.createElement("button",{className:"wprf-repeater-button",onClick:function(){return l.setFieldValue(a,[].concat(_toConsumableArray(r),[{index:v4()}]))}},null==t?void 0:t.label)))},Slider=function(t){var n=t.name,e=t.id,r=t.label,a=t.units,l=t.min,o=t.max,i=t.unit,c=t.reset,u=_slicedToArray(React.useState(t.value||0),2),s=u[0],d=u[1],u=_slicedToArray(React.useState(i),2),p=u[0],f=u[1];return React.useEffect(function(){var e;s&&(isNumber(s)?e=p?"".concat(s).concat(p):"".concat(s):isString(s)&&(e=-1<s.indexOf(p)?"".concat(s):"".concat(s).concat(p)),t.onChange({target:{type:"slider",name:n,value:e}}))},[s,p]),React.createElement("div",{className:"wprf-slider-wrap"},React.createElement("div",{className:"wprf-slider-control-head"},React.createElement(Label,{htmlFor:e||n},r),isArray(a)&&0<a.length&&React.createElement("div",{className:"wprf-slider-units"},a.map(function(e,t){return React.createElement(components.Button,{key:t,isSmall:!0,isPrimary:!0,onClick:function(){return f(e)},className:e==p?"unit-active":""},e)}))),React.createElement("div",{className:"wprf-slider-control"},React.createElement(components.RangeControl,{allowReset:null==c||c,value:parseInt(s),min:l,max:o,onChange:function(e){return d(e)}})))},SelectAsync=function(a){function l(t,n){var e;if(a.ajax&&(!a.ajax.rules||when(a.ajax.rules,o.values)))if(t)if(t.length<3)n([{label:"Please type 3 or more characters.",value:null,disabled:!0}]);else{var r={inputValue:t};if(null!=(e=Object.keys(a.ajax.data))&&e.map(function(e){var t,n;-1<a.ajax.data[e].indexOf("@")?(n=a.ajax.data[e].substr(1),r[e]=null==(t=o.values)?void 0:t[n]):r[e]=a.ajax.data[e]}),!f&&t)return m(!0),window.lastRequest=null,wpFetch({path:a.ajax.api,data:r}).then(function(e){return n(e),e}).finally(function(){var e;m(!1),window.lastRequest&&(e=window.lastRequest,window.lastRequest=null,l.apply(void 0,_toConsumableArray(e))),window.lastCompleteRequest=t});window.lastRequest=[t,n]}else n(u)}var o=useBuilderContext(),e=a.id,t=a.name,n=a.multiple,r=a.placeholder,i=a.onChange,c=_slicedToArray(React.useState(o.eligibleOptions(a.options)),2),u=c[0],s=c[1],c=_slicedToArray(React.useState(null==a?void 0:a.value),2),d=c[0],p=c[1],c=_slicedToArray(React.useState(!1),2),f=c[0],m=c[1];return React.useEffect(function(){s(o.eligibleOptions(a.options))},[o.values.source]),React.useEffect(function(){i({target:{type:"select",name:t,value:d,multiple:n}})},[d]),React.createElement("div",{className:"wprf-async-select-wrapper"},React.createElement(AsyncSelect__default.default,{cacheOptions:!0,loadOptions:l,defaultOptions:u,isDisabled:null==a?void 0:a.disable,isMulti:null!=n&&n,classNamePrefix:"wprf-async-select",id:e,name:t,placeholder:r,formatOptionLabel:function(e,t){var n,r;if(null!=t&&null!=(n=t.inputValue)&&n.length&&e.name&&e.name.toLowerCase().includes(null==t||null==(n=t.inputValue)?void 0:n.toLowerCase()))return null!=e&&e.name,n=new RegExp("(".concat(null==t?void 0:t.inputValue,")"),"gi"),t=null==(t=e.name)?void 0:t.replace(n,"<strong style={font-weight: 900}>$1</strong>"),r=null==(r=e.address)?void 0:r.replace(n,"<strong style={font-weight: 900}>$1</strong>"),React.createElement(React.Fragment,null,parse__default.default(t||"")," ",React.createElement("small",null,parse__default.default(r||"")));return React.createElement(React.Fragment,null,e.name?React.createElement(React.Fragment,null,React.createElement("b",null,e.name)," "):React.createElement(React.Fragment,null,e.label," "),e.address&&React.createElement("small",null,e.address))},value:d,isClearable:!0,isOptionDisabled:function(e){return null==e?void 0:e.disabled},onChange:function(e){return p(e)}}))},SelectAsync$1=withLabel(SelectAsync),ColorPicker=function(e){var t,n=e.value,r=e.name,a=e.id,l=e.onChange,e=_slicedToArray(React.useState(!1),2),o=e[0],i=e[1],e=_slicedToArray(React.useState(n||null),2),c=e[0],u=e[1],e=_slicedToArray(React.useState(null),2),s=e[0],d=e[1],e=React.useRef(null);React.useEffect(function(){d(n)},[]);return React.useEffect(function(){l({target:{type:"colorpicker",name:r,value:c}})},[c]),t=e,React.useEffect(function(){function e(e){t.current&&!t.current.contains(e.target)&&i(!1)}return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}},[t]),React.createElement(React.Fragment,null,React.createElement("div",{className:"wprf-colorpicker-wrap",ref:e},React.createElement("input",{type:"hidden",value:n,name:r,id:a}),React.createElement("span",{className:"wprf-picker-display",style:{backgroundColor:n,borderColor:n},onClick:function(){return i(!o)}}),o&&React.createElement(React.Fragment,null,React.createElement("button",{className:"wprf-colorpicker-reset",onClick:function(e){e.preventDefault(),u(s),i(!1)}},i18n.__("Reset","notificationx")),React.createElement(components.ColorPicker,{color:n,onChangeComplete:function(e){return u(e.hex)}}))))},ColorPicker$1=withLabel(ColorPicker),Action=function(e){return React.createElement(React.Fragment,null,hooks.applyFilters(e.action,"",e))},Media=function(n){var e=_slicedToArray(React.useState(null!=(e=n.value)&&e.url?n.value:null),2),r=e[0],a=e[1],l=useBuilderContext();return React.useEffect(function(){n.onChange({target:{type:"media",name:n.name,value:r}})},[r]),React.createElement("div",{className:"wprf-control wprf-media"},null!=r&&!(null!=n&&n.notImage)&&React.createElement("div",{className:"wprf-image-preview"},null!=r&&(null==r?void 0:r.url)&&React.createElement("img",{src:r.url,alt:r.title})),React.createElement("div",{className:"wprf-image-uploader"},React.createElement(mediaUtils.MediaUpload,{onSelect:function(e){a({id:e.id,title:e.title,url:e.url})},multiple:!1,allowedTypes:["image"],value:r,render:function(e){var t=e.open;return React.createElement(React.Fragment,null,null!=r&&React.createElement("button",{className:"wprf-btn wprf-image-remove-btn",onClick:function(){null!=n&&n.is_pro&&!l.is_pro_active?l.alerts.pro_alert(null==n?void 0:n.popup).fire():a(null)}},(null==n?void 0:n.remove)||"Remove"),React.createElement("button",{className:"wprf-btn wprf-image-upload-btn",onClick:function(){null!=n&&n.is_pro&&!l.is_pro_active?l.alerts.pro_alert(null==n?void 0:n.popup).fire():t()}},null!=r?(null==n?void 0:n.reset)||"Change Image":(null==n?void 0:n.button)||"Upload"))}})))},Media$1=withLabel(Media),Editor=function(n){var e=_slicedToArray(React.useState(draftJs.EditorState.createEmpty()),2),t=e[0],r=e[1];return React.useEffect(function(){var e,t;n.value&&(e=(t=htmlToDraft__default.default(n.value)).contentBlocks,e=draftJs.ContentState.createFromBlockArray(e,t.entityMap),t=draftJs.EditorState.createWithContent(e),r(t))},[]),React.useEffect(function(){var e=draftToHtml__default.default(draftJs.convertToRaw(t.getCurrentContent()));n.onChange({target:{type:"editor",value:e,name:n.name}})},[t]),React.createElement(reactDraftWysiwyg.Editor,{placeholder:null==n?void 0:n.placeholder,toolbar:toolbarOptions,editorState:t,toolbarClassName:"wprf-editor-toolbar",wrapperClassName:"wprf-editor wprf-control",editorClassName:"wprf-editor-main",onEditorStateChange:r})},Editor$1=withLabel(Editor),Button=function(n){var e,t,r,a;if(null!=n&&n.text||!0===(null==n?void 0:n.group))return e=validFieldProps(n,["is_pro","visible","disable","parentIndex","context","onBlur","value","ajax","text"]),t=(a=_slicedToArray(React.useState(!1),2))[0],r=a[1],null!=n&&n.href?React.createElement("a",{href:-1===(null==n?void 0:n.href)?null==n?void 0:n.value:null==n?void 0:n.href,target:null==n?void 0:n.target,className:classNames__default.default("wprf-control wprf-button wprf-href-btn",null==n?void 0:n.classes)},null==n?void 0:n.text):null!=n&&n.group?(a=n.fields.map(function(e,t){t=[].concat(_toConsumableArray(n.parentIndex),["fields",t]);return React.createElement(Field$1,_extends$1({key:e.name},e,{parentIndex:t}))}),React.createElement("div",{className:"wprf-control wprf-button-group wprf-flex"},a)):React.createElement(React.Fragment,null,React.createElement("button",_extends$1({},e,{name:n.name,disabled:t,onClick:null!=(a=null==n?void 0:n.onClick)?a:function(e){null!=n&&n.ajax&&(r(!0),hitAAJX(n.ajax,n.context).then(function(e){var t;if(r(!1),"error"==(null==e?void 0:e.status))throw new Error(null==e?void 0:e.message);n.onChange({target:{type:"button",name:n.name,value:!0}}),null!=(e=n.ajax)&&e.hideSwal||(e=(null==(e=n.ajax)||null==(e=e.swal)?void 0:e.icon)||"success",t=(null==(t=n.ajax)||null==(t=t.swal)?void 0:t.text)||"Complete",n.context.alerts.toast(e,t,{autoClose:null==(e=n.ajax)||null==(e=e.swal)?void 0:e.autoClose})),null!=(t=n.ajax)&&t.reload&&setTimeout(function(){return window.location.reload()},1e3)}).catch(function(e){var t;console.error("Error In Button Called",n.name,e),r(!1),n.onChange({target:{type:"button",name:n.name,value:!1}}),null!=(t=n.ajax)&&t.hideSwal||n.context.alerts.toast("error",(null==e?void 0:e.message)||i18n.__("Something went wrong.","notificationx"))})),useTrigger(n)},className:classNames__default.default("wprf-control wprf-button wprf-btn",null==n?void 0:n.classes)}),(null==n?void 0:n.icon)&&(isObject(n.icon)?null==n||null==(e=n.context)||null==(e=e.icons)||null==(e=e[null==n||null==(a=n.icon)?void 0:a.type])?void 0:e[null==n||null==(a=n.icon)?void 0:a.name]:""),isObject(null==n?void 0:n.text)&&null!=n&&n.ajax?t?null==n||null==(e=n.text)?void 0:e.loading:n.value?null==n||null==(a=n.text)?void 0:a.saved:null==n||null==(t=n.text)?void 0:t.normal:null==n?void 0:n.text));throw new Error(i18n.__("Button has a required params #text.","notificationx"))},Button$1=withLabel(Button);function ownKeys$1(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$1(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$1(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$1(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var ResponsiveNumber=function(t){var n=validFieldProps(t,["is_pro","visible","trigger","disable","parentIndex","context","badge","popup"]),e=_slicedToArray(React.useState(Object.keys(t.controls)[0]),2),r=e[0],a=e[1],e=n.value;isObject(n.value)||Object.keys(t.controls).reduce(function(e,t){return _objectSpread$1(_objectSpread$1({},e),{},_defineProperty({},t,n.value))},{});var e=_slicedToArray(React.useState(e),2),l=e[0],o=e[1];return React.useEffect(function(){n.onChange({target:{type:"input",name:n.name,value:l,checked:null,multiple:null}})},[l]),React.createElement("div",{style:{display:"flex",alignItems:"center",rowGap:5,columnGap:10,flexWrap:"wrap"}},React__default.default.createElement("input",_objectSpread$1(_objectSpread$1({},n),{},{type:"number",value:null==l?void 0:l[r],onChange:function(e){o(_objectSpread$1(_objectSpread$1({},l),{},_defineProperty({},r,e.target.value)))}})),React.createElement("div",{style:{display:"flex",alignItems:"center"}},null==(e=Object.keys(t.controls))?void 0:e.map(function(e){return React.createElement("button",{type:"button",key:e,className:"responsive-button ".concat(r===e?"active":""),onClick:function(){return a(e)}},React.createElement("img",{src:t.controls[e].icon,alt:"desktop",style:{width:t.controls[e].size}}))})))},ResponsiveNumber$1=(ResponsiveNumber.defaultProps={type:"number"},withLabel(React__default.default.memo(ResponsiveNumber))),eligibleMessage=function(e){if(null!=e&&e.messages)for(var t in e.messages){t=e.messages[t];if(when(t.rules,e.context.values))return t}return{message:null==e?void 0:e.message,html:null==e?void 0:e.html,type:"normal"}},Message=function(e){var t=eligibleMessage(e),n=t.html,r=t.message,t=t.type;return r?React.createElement("div",{className:classNames__default.default("wprf-control","wprf-message","wprf-".concat(void 0===t?"warning":t,"-message"),"wprf-".concat(e.name,"-message"),null==e?void 0:e.classes)},n&&React.createElement("p",{dangerouslySetInnerHTML:{__html:r}}),!n&&React.createElement("p",null,r)):React.createElement(React.Fragment,null)},Modal=function(n){var e;if(null==(null==n?void 0:n.body)||null==(null==n?void 0:n.button))throw new Error(i18n.__("Modal needs button/body with it.","notificationx"));function r(){return i(!0)}function t(){return i(!1)}function a(){var e=null==(e=n.context.values)?void 0:e[n.cancel];null!=n&&n.cancel&&e&&e!==s&&t()}var l=_slicedToArray(React.useState(!1),2),o=l[0],i=l[1],l=_slicedToArray(React.useState(!1),2),l=l[0],c=React.useCallback(function(){},[]),u=React.useRef(),s=(React.useEffect(function(){var e;u.current=null==(e=n.context.values)?void 0:e[n.cancel]}),u.current);return React.createElement("div",{className:"wprf-control wprf-modal",id:"wprf-modal-".concat(n.name)},!(null!=n&&n.close_on_body)&&React.createElement(GenericField,_extends$1({type:"button"},null==n?void 0:n.button,{onClick:r})),(null==n?void 0:n.show_body)&&React.createElement("div",{className:"wprf-control wprf-modal-show-body"},null==n||null==(e=n.body)||null==(e=e.fields)?void 0:e.map(function(e){var t;return"text"===e.type?React.createElement("div",{className:"wprf-control wprf-modal-body-value-heading"},React.createElement("h4",{key:e.name},(null==(t=n.context.values)?void 0:t[e.name])||(null==e?void 0:e.default)),(null==n?void 0:n.close_on_body)&&React.createElement(GenericField,_extends$1({type:"button"},null==n?void 0:n.button,{onClick:r}))):"textarea"===e.type?React.createElement("p",{key:e.name},(null==(t=n.context.values)?void 0:t[e.name])||(null==e?void 0:e.default)):null})),o&&React.createElement(SweetAlert__default.default,{customClass:"wprf-modal-inner",style:{maxWidth:"900px",width:"100%",overflowY:"scroll",margin:"50px auto"},closeBtnStyle:{top:"5px",right:"5px",color:"#f78c8c",fontSize:"18px",border:"1px solid #f78c8c",borderRadius:"50%",width:"30px",height:"30px",display:"inline-flex",alignItems:"center",justifyContent:"center"},title:React.createElement(ModalHeader,{content:null==n||null==(e=n.body)?void 0:e.header}),onConfirm:c,showConfirm:!1,showCloseButton:!0,closeOnClickOutside:!0,onCancel:t,afterUpdate:function(){return a}},React.createElement(ModalContent,_extends$1({},n,{isLoading:l,closeModal:t,context:n.context,onConfirm:c}))))},InnerContent=function(e){var t=e.fields,r=e.parentIndex,n=e.context,e=_slicedToArray(React.useState([]),2),a=e[0],l=e[1],e=_slicedToArray(React.useState([]),2),o=e[0],i=e[1];return React.useEffect(function(){var e=sortingFields(t);n.setFormField([r,"fields"],e),l(e)},[]),React.useEffect(function(){var e;isArray(a)&&0<a.length&&(e=a.map(function(e,t){var n=[].concat(_toConsumableArray(r),["fields",t]);return"section"===(null==e?void 0:e.type)?React.createElement(GenericField,_extends$1({key:"input-".concat(e.name,"-").concat(t)},e,{parentIndex:n})):e?React.createElement(Field$1,_extends$1({key:"input-".concat(e.name,"-").concat(t)},e,{parentIndex:n})):React.createElement(React.Fragment,null)}),i(e))},[a]),React.createElement(React.Fragment,null,o)};function _objectDestructuringEmpty(e){if(null==e)throw new TypeError("Cannot destructure "+e)}var Submit=function(e){var e=_extends$1({},(_objectDestructuringEmpty(e),e)),n=useBuilderContext(),e=(null==e?void 0:e.label)||i18n.__("Save Changes","notificationx"),t=React.useCallback(function(e){var t;null!=(t=n.submit)&&t.onSubmit&&n.submit.onSubmit(e,n)},[n]);return React.createElement("div",{className:"wprf-submit wprf-control"},React.createElement(components.Button,{className:"wprf-submit-button",onClick:t},e))},SteppedButton=function(n){var e=_slicedToArray(React.useState(void 0),2),r=e[0],a=e[1],e=_slicedToArray(React.useState(void 0),2),l=e[0],o=e[1];return useBuilderContext(),React.useEffect(function(){var e=n.fields.map(function(e){return e.id}),t=e.findIndex(function(e){return e===n.active});-1!=t&&o(e[t-1]),t<=e.length&&a(e[t+1])},[n.active,n.fields]),React.createElement("div",{className:"wprf-stepped-button"},n.config.buttons&&Object.keys(n.config.buttons).map(function(e,t){return React.createElement(React__default.default.Fragment,{key:"button_".concat(e,"_").concat(t)},("next"===e&&void 0!==r||"prev"===e&&void 0!==l)&&React.createElement(components.Button,{className:"wprf-btn wprf-step-btn-".concat(e),onClick:function(){return n.setActive("next"===e?r:l)}},null==(t=n.config.buttons)?void 0:t[e]),null==r&&(null==(t=n.config.buttons)||null==(t=t[e])?void 0:t.type)&&React.createElement(Field$1,null==(t=n.config.buttons)?void 0:t[e]))}))},SteppedButton$1=React__default.default.memo(SteppedButton),_excluded=["fields","active","setActive","submit"],Content=function(e){var t=e.fields,r=e.active,n=e.setActive,a=e.submit,l=_objectWithoutProperties(e,_excluded);if(void 0===t)throw new Error(i18n.__("There are no #tabs args defined in props.","notificationx"));var o,i,c=useBuilderContext(),u=l.parentIndex||[];if(isArray(t))return o=(e=_slicedToArray(React.useState([]),2))[0],i=e[1],React.useEffect(function(){var e=t.filter(function(e){return isVisible(null==c?void 0:c.values,e)});i(e)},[t,null==c||null==(e=c.values)?void 0:e.source]),React.createElement("div",{className:classNames__default.default("wprf-tab-content-wrapper",null==c||null==(e=c.values)?void 0:e.source,null==c||null==(e=c.values)?void 0:e.themes)},React.createElement("div",{className:"wprf-tab-flex"},React.createElement("div",{className:"wprf-tab-contents"},t.map(function(e,t){var n;return isVisible(null==c?void 0:c.values,e)?(n=classNames__default.default("wprf-tab-content","wprf-tab-".concat(null==e?void 0:e.id),{"wprf-active":r===e.id}),React.createElement("div",{id:null==e?void 0:e.id,className:n,key:null==e?void 0:e.id},((null==e?void 0:e.label)&&(null==(n=null==l?void 0:l.title)||n)||(null==l?void 0:l.content_heading))&&React.createElement("div",{className:"wprf-tab-heading-wrapper"},(null==e?void 0:e.label)&&(null==(n=null==l?void 0:l.title)||n)&&React.createElement("h4",null,e.label),React.createElement("div",null,(null==l?void 0:l.content_heading)&&Object.keys(l.content_heading).map(function(e,t){return React.createElement(React__default.default.Fragment,{key:"button_".concat(e,"_").concat(t)},React.createElement(Field$1,l.content_heading[e]))}))),React.createElement(InnerContent,{context:c,fields:null==e?void 0:e.fields,parentIndex:[].concat(_toConsumableArray(u),[t])}))):""})),hooks.applyFilters("wprf_tab_content","",l)),(null==l||null==(e=l.step)?void 0:e.show)&&React.createElement(SteppedButton$1,{fields:o,active:r,setActive:n,config:null!=(e=l.step)?e:{show:!1}}),(null==(o=null==a?void 0:a.show)||o)&&(null==a||!a.rules||when(null==a?void 0:a.rules,{rest:l}))&&React.createElement(Submit,a));throw new Error(i18n.__("Not an array.","notificationx"))},Tab=function(e){var t=useBuilderContext(),n=_slicedToArray(React.useState(e.value||e.active),2),r=n[0],a=n[1],n=classNames__default.default("wp-react-form wprf-tabs-wrapper",null==e?void 0:e.className,{"wprf-tab-menu-as-sidebar":null==e?void 0:e.sidebar});return React.useEffect(function(){e.value!==r&&a(e.value)},[e.value]),React.useEffect(function(){e.value!==r&&e.onChange({target:{type:"button",name:e.name,value:r}})},[r]),React.createElement("div",{className:n},React.createElement(Menu,_extends$1({},e,{active:r,setActive:function(e){return a(e)},fields:e.fields,context:t})),React.createElement(Content,_extends$1({},e,{fields:e.fields,active:r,setActive:function(e){return a(e)},submit:null==e?void 0:e.submit})))};function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}data.registerStore("formbuilder",store);var FormBuilder=function(e){var t,n=useBuilderContext(),r=e;return null!=(t=r)&&t.type||(r=_objectSpread(_objectSpread(_objectSpread({},e),e.config),{},{value:e.config.active,fields:e.tabs,tabs:void 0,submit:null==e?void 0:e.submit,onChange:function(e){n.setActiveTab(null==e||null==(e=e.target)?void 0:e.value)}})),React.createElement(React.Fragment,null,React.createElement(Tab,r))};exports.Action=Action,exports.BuilderConsumer=BuilderConsumer,exports.BuilderProvider=BuilderProvider,exports.Button=Button$1,exports.CodeViewer=CodeViewer$1,exports.ColorPicker=ColorPicker$1,exports.Column=Column,exports.Date=Date$1,exports.Editor=Editor$1,exports.Field=Field$1,exports.FormBuilder=FormBuilder,exports.GenericField=GenericField,exports.GenericInput=GenericInput,exports.Group=Group$1,exports.Image=Image,exports.Input=Input$1,exports.JsonUploader=JsonUploader$1,exports.Label=Label,exports.Media=Media$1,exports.Message=Message,exports.Modal=Modal,exports.ObjectFilter=ObjectFilter,exports.Radio=Radio,exports.Repeater=Repeater,exports.ResponsiveNumber=ResponsiveNumber$1,exports.Row=Row,exports.Section=Section$1,exports.Select=Select$1,exports.SelectAsync=SelectAsync$1,exports.Slider=Slider,exports.SweetAlert=SweetAlert,exports.Textarea=Textarea$1,exports.Toggle=Toggle,exports._extends=_extends,exports.builderReducer=builderReducer,exports.downloadFile=downloadFile,exports.executeChange=executeChange,exports.getIn=getIn,exports.getSelectedValues=getSelectedValues,exports.getStoreData=getStoreData,exports.getTime=getTime,exports.hitAAJX=hitAAJX,exports.isArray=isArray,exports.isEmptyObj=isEmptyObj,exports.isExists=isExists,exports.isFunction=isFunction,exports.isNumber=isNumber,exports.isObject=isObject,exports.isString=isString,exports.isVisible=isVisible,exports.merge=merge,exports.objectWithoutPropertiesLoose=objectWithoutPropertiesLoose,exports.processAjaxData=processAjaxData,exports.setIn=setIn,exports.setStoreData=setStoreData,exports.sortingFields=sortingFields,exports.triggerDefaults=triggerDefaults,exports.useBuilder=useBuilder,exports.useBuilderContext=useBuilderContext,exports.useDefaults=useDefaults,exports.validFieldProps=validFieldProps,exports.when=when,exports.withLabel=withLabel,exports.withProps=withProps,exports.withState=withState,exports.wpFetch=wpFetch;
