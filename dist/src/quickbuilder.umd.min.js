((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("@wordpress/data"),require("lodash-es"),require("@wordpress/api-fetch"),require("@wordpress/date"),require("moment"),require("intersect"),require("@wordpress/i18n"),require("classnames"),require("@wordpress/hooks"),require("sweetalert2"),require("@wordpress/components"),require("copy-to-clipboard"),require("react-select"),require("react-sortablejs"),require("html-react-parser"),require("react-select/async"),require("@wordpress/media-utils"),require("react-draft-wysiwyg"),require("draft-js"),require("draftjs-to-html"),require("html-to-draftjs"),require("react-draft-wysiwyg/dist/react-draft-wysiwyg.css"),require("react-bootstrap-sweetalert")):"function"==typeof define&&define.amd?define(["exports","react","@wordpress/data","lodash-es","@wordpress/api-fetch","@wordpress/date","moment","intersect","@wordpress/i18n","classnames","@wordpress/hooks","sweetalert2","@wordpress/components","copy-to-clipboard","react-select","react-sortablejs","html-react-parser","react-select/async","@wordpress/media-utils","react-draft-wysiwyg","draft-js","draftjs-to-html","html-to-draftjs","react-draft-wysiwyg/dist/react-draft-wysiwyg.css","react-bootstrap-sweetalert"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).quickbuilder={},e.React,e.wpData,e.lodashEs,e.wpApiFetch,e.wpDate,e.momentLib,e.intersect,e.wpI18n,e.classNames,e.wpHooks,e.sweetalert2,e.wpComponents,e.copy,e.reactSelect,e.reactSortablejs,e.parse,e.AsyncSelect,e.wpMedia,e.reactDraftWysiwyg,e.draftJs,e.draftjsToHtml,e.htmlToDraftjs,0,e.sweetalert)})(this,function(e,C,x,S,L,M,B,U,d,q,H,G,m,z,W,$,J,X,Y,K,Q,Z,ee,te,ne){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=t(C),re=t(L),le=t(B),ae=t(U),p=t(q),oe=t(G),ie=t(z),ue=t(W),ce=t(J),se=t(X),fe=t(Z),de=t(ee),pe=t(ne);function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function me(e){e=((e,t)=>{if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);if("object"!=o(n=n.call(e,t||"default")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string");return"symbol"==o(e)?e:e+""}function E(e,t,n){return(t=me(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ve(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function be(e,t){var n;if(e)return"string"==typeof e?ve(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ve(e,t):void 0}function w(e){return(e=>{if(Array.isArray(e))return ve(e)})(e)||(e=>{if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)})(e)||be(e)||(()=>{throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function ye(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function ge(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ye(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ye(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function Ee(e){return null!==e&&"number"==typeof e}function we(e){return null!==e&&"function"==typeof e}function he(e,t){var n=t,r=e;if(!k(e)){e.persist&&e.persist();var e=e.target||e.currentTarget,l=e.type,a=e.value,o=e.checked,i=e.multiple,n=t||e.name;switch(l){case"number":case"range":r=parseFloat(a);break;case"checkbox":r=i?a:o;break;default:r=a}}return{field:n,val:r}}function n(e,t,n){for(var r=S.clone(e),l=r,a=0,o=S.toPath(t);a<o.length-1;a++){var i=o[a],u=T(e,o.slice(0,a+1));l=u&&(h(u)||Array.isArray(u))?l[i]=S.clone(u):(u=o[a+1],l[i]=Pe(u)&&0<=Number(u)?[]:{})}return(0===a?e:l)[o[a]]===n?e:(void 0===n?delete l[o[a]]:l[o[a]]=n,0===a&&void 0===n&&delete r[o[a]],r)}function P(e){var t=e.type,n=["validation_rules","default","rules","meta","switch"].concat(w(1<arguments.length&&void 0!==arguments[1]?arguments[1]:[])),t=("select"!==t&&"select-async"!==t&&"radio-card"!==t&&"checkbox"!==t&&"toggle"!==t&&e.multiple&&n.push("options"),"tab"!==t&&"group"!==t&&"repeater"!==t&&"section"!==t&&"button"!==t&&n.push("fields"),ke(e,n));return null==e||!e.label||null!=e&&e.placeholder||(t.placeholder=e.label),t}function Oe(l){var a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(null!==a&&l){var r,e=!0;if(e=null!=l&&l.rules?I(null==l?void 0:l.rules,a.values):e)return r={},Object.keys(l.data).map(function(e){var t,n;-1<(null==(n=(t=l.data[e]).indexOf)?void 0:n.call(t,"@"))?(n=l.data[e].substr(1),r[e]=null==(t=a.values)?void 0:t[n]):r[e]=l.data[e]}),Ce({path:l.api,data:r}).then(function(e){"success"==(null==e?void 0:e.status)&&null!=e&&e.redirect&&(window.location=null==e?void 0:e.redirect);var t,n,r=null!=e&&null!=(t=e.data)&&t.context?e.data.context:!(null==e||!e.context)&&e.context;return r&&h(r)&&Object.keys(r).map(function(e){a.setFieldValue(e,r[e])}),null!=e&&null!=(t=e.data)&&t.download&&Ne({data:JSON.stringify(e.data.download),fileName:(null==e||null==(t=e.data)?void 0:t.filename)||"export.json",fileType:"text/json"}),null!=l&&l.trigger&&k(null==l?void 0:l.trigger)&&(t=l.trigger.indexOf("@"),n=l.trigger.indexOf(":"),0===t)&&0<n&&(t=l.trigger.substr(1,n-1),"true"==(n=l.trigger.substr(n+1))?n=!0:"false"==n&&(n=!1),a.setFieldValue(t,n)),e})}return Promise.reject(!1)}function je(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=M.__experimentalGetSettings();return le.default.utc(e||void 0).utcOffset(+(null==n||null==(e=n.timezone)?void 0:e.offset),t)}function _e(e,t,n){var r=w(e),e=t.filter(function(t){return r.findIndex(function(e){return e[n]===t[n]})<=-1});return[].concat(w(r),w(e))}function xe(e){return("function"!=typeof Symbol||"symbol"!==o(Symbol.iterator))&&e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":o(e)}function l(e,t){var n=!(2<(arguments.length<=2?0:arguments.length-2)&&void 0!==(arguments.length<=4?void 0:arguments[4]))||arguments.length<=4?void 0:arguments[4];return String.prototype.split.call(t,/[,[\].]+?/).filter(Boolean).reduce(function(e,t){return e&&Object.hasOwnProperty.call(e,t)?e[t]:n},e)}function Se(e,n){var t,r;return Fe(e)?(t=e.slice(0,1)[0],r=e.slice(1).map(function(e,t){return(Fe(e)?I:Ie)(e,n)}),Ae(t,r)):Ie(e,n)}var Ce=function(e){e=ge(ge({},e),{},{method:"POST"});return re.default(e)},k=function(e){return null!==e&&"string"==typeof e},Pe=function(e){return String(Math.floor(Number(e)))===e},N=function(e){return null!==e&&"object"===o(e)&&Array.isArray(e)},h=function(e){return null!==e&&"object"===o(e)&&!N(e)},D=function(e,t){return null==t||!t.rules||null==t.name||(t=I(t.rules,e),Boolean(t))},v=function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},T=function(e,t,n,r){void 0===r&&(r=0);for(var l=S.toPath(t);e&&r<l.length;)e=e[l[r++]];return void 0===e?n:e},F=function(e){return[].concat(e).sort(function(e,t){return null==e.priority||null==t.priority?0:e.priority>t.priority?1:-1})},ke=function(e,t){if(null==e)return{};for(var n,r={},l=Object.keys(e),a=0;a<l.length;a++)n=l[a],0<=t.indexOf(n)||(r[n]=e[n]);return r},Ne=function(e){var t=e.data,n=e.fileName,t=new Blob([t],{type:e.fileType}),e=document.createElement("a"),n=(e.download=n,e.href=window.URL.createObjectURL(t),new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}));e.dispatchEvent(n),e.remove()},De={is:function(e,t,n){return l(n,e)==t},"!is":function(e,t,n){return!De.is(e,t,n)},includes:function(e,t,n){if(!v(n)){n=l(n,e);if("function"!=xe(n)){if(N(t)&&N(n))return!(null==(e=ae.default(n,t))||!e.length);if(N(t)&&"string"==xe(n))return t.includes(n);if(N(n)&&"string"==xe(t))return n.includes(t)}}return!1},"!includes":function(e,t,n){return!De.includes(e,t,n)},isOfType:function(e,t,n){return xe(l(n,e))===t},"!isOfType":function(e,t,n){return!De.isOfType(e,t,n)},allOf:function(e,t,n){var r;if(Array.isArray(t))return r=l(n,e),t.every(function(e){return r.includes(e)});throw Error(d.__('"allOf" condition requires an array as #3 argument',"notificationx"))},anyOf:function(e,t,n){if(Array.isArray(t))return n=l(n,e),t.includes(n);throw Error(d.__('"anyOf" condition requires an array as #3 argument',"notificationx"))},gt:function(e,t,n){return l(n,e)>t},gte:function(e,t,n){return l(n,e)>=t},lt:function(e,t,n){return l(n,e)<t},lte:function(e,t,n){return l(n,e)<=t}},Te={and:function(e){return!e.includes(!1)},or:function(e){return e.includes(!0)},not:function(e){if(1!==e.length)throw Error(d.__('"not" can have only one comparison rule, multiple rules given',"notificationx"));return!e[0]}},Fe=function(e){return!!(Array.isArray(e)&&Array.isArray(e[1])&&e[0]&&Te[e[0].toLowerCase()])},Ie=function(e,t){var n=e[0],r=e[1],e=e[2];if("string"!=typeof n||void 0===De[n])throw Error(d.sprintf(d.__("Invalid comparison rule %s.","notificationx"),n));return De[n](r,e,t)},Ae=function(e,t){return Te[e.toLowerCase()](t)},I=function(e,t){return"function"==typeof e?Promise.resolve(e(t)):Se(e,t)};function Ve(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ve(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ve(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Re={savedValues:{type:"conversions",source:"edd"},values:{},touched:{},errors:{}},L={reducer:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:Re,t=1<arguments.length?arguments[1]:void 0;switch(t.type){case"SET_SAVED_VALUES":var n=a({},e);return a(a({},n),{},{values:t.payload,savedValues:t.payload});case"FIELD_VALUE":var n=a({},e),r=t.payload;return a(a({},n),{},{values:a(a({},null==(n=n)?void 0:n.values),r)});case"REMOVE_FIELD_VALUE":n=a({},e),r=t.payload;return null!=(l=n.values)&&l[r]&&delete n.values[r],n;case"RESET_FIELD_VALUE":var l=a({},e);return null!=(r=l.values)&&r[t.payload]&&(delete l.values[t.payload],null!=(n=l.savedValues))&&n[t.payload]&&(l.values[t.payload]=l.savedValues[t.payload]),l;case"FIELD_ERROR":return a(a({},e),{},{errors:a(a({},e.errors),t.payload)});case"REMOVE_FIELD_ERROR":r=a({},e);return delete r.errors[t.payload],r;case"FIELD_TOUCHED":return a(a({},e),{},{touched:a(a({},e.touched),t.payload)})}return e},actions:{setSavedValues:function(e){return{type:"SET_SAVED_VALUES",payload:e}},setFieldValue:function(e){return{type:"FIELD_VALUE",name:e.name,payload:e.value}},removeFieldValue:function(e){return{type:"REMOVE_FIELD_VALUE",payload:e}},resetFieldValue:function(e){return{type:"RESET_FIELD_VALUE",payload:e}},setFieldTouched:function(e){return{type:"FIELD_TOUCHED",payload:e}},setError:function(e){return{type:"FIELD_ERROR",payload:e}},removeError:function(e){return{type:"REMOVE_FIELD_ERROR",payload:e}}},selectors:{getValues:function(e){return e.values},getFieldValue:function(e,t){return null==(e=e.values)?void 0:e[t]},getSavedFieldValue:function(e,t,n){var r;return(null===n||(null==(r=e.savedValues)?void 0:r[n])===(null==(r=e.values)?void 0:r[n]))&&(null==(r=e.savedValues)?void 0:r[t])},isTouched:function(e,t){return null==(e=e.touched)?void 0:e[t]},getError:function(e,t){return null==(e=e.errors)?void 0:e[t]},isVisible:function(e,t){return!t.rules||null==t.name||(t=I(t.rules,e.values),Boolean(t))}}};function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)!{}.hasOwnProperty.call(r,n)||(e[n]=r[n])}return e}).apply(null,arguments)}function A(e,t){return(e=>{if(Array.isArray(e))return e})(e)||((e,t)=>{var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,l,a,o,i=[],u=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=a.call(n)).done)&&(i.push(r.value),i.length!==t);u=!0);}catch(e){c=!0,l=e}finally{try{if(!u&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw l}}return i}})(e,t)||be(e,t)||(()=>{throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function Le(r){if(void 0===r.fields)throw new Error(d.__("There are no tabs defined!","notificationx"));var l=r.active,a=r.setActive,t=r.fields,o=r.context,e=(i=A(C.useState([]),2))[0],n=i[1],i=(null!=r&&r.dataShare&&Me(o,t),C.useEffect(function(){var e=t.filter(function(e){return D(null==o?void 0:o.values,e)});n(e)},[t,null==o||null==(i=o.values)?void 0:i.source]),p.default("wprf-tab-menu-wrapper",null==r?void 0:r.className,{"wprf-tab-menu-sidebar":null==r?void 0:r.sidebar},null==o||null==(i=o.values)?void 0:i.source)),u=e.findIndex(function(e){return e.id===l});return C.createElement("div",{className:i},C.createElement("ul",{className:"wprf-tab-nav"},e.map(function(t,e){var n;return C.createElement("li",{className:p.default("wprf-tab-nav-item",E(E(E({},"".concat(t.classes),t.classes),"wprf-active-nav",l===t.id),"wprf-tab-complete",!(null==r||!r.completionTrack)&&e<=u)),"data-key":t.id,key:t.id,onClick:function(){var e;return(null==(e=null==r?void 0:r.clickable)||e)&&a(t.id)}},(null==t?void 0:t.icon)&&(k(t.icon)&&!h(t.icon)?C.createElement("img",{src:t.icon,alt:null==t?void 0:t.label}):h(t.icon)?null==o||null==(e=o.icons)||null==(e=e[null==t||null==(n=t.icon)?void 0:n.type])?void 0:e[null==t||null==(n=t.icon)?void 0:n.name]:""),C.createElement("span",null,t.label),(null==r?void 0:r.dataShare)&&C.createElement("span",{className:"list-count"},(null==t?void 0:t.count)<10?"0".concat(null==t?void 0:t.count):null==t?void 0:t.count))})))}var Me=function(t,e){var n=["necessary_cookie_lists","functional_cookie_lists","analytics_cookie_lists","performance_cookie_lists","advertising_cookie_lists","uncategorized_cookie_lists"].map(function(e){return Array.isArray(null==t?void 0:t.values[e])?null==t||null==(e=t.values[e])?void 0:e.length:0});e.forEach(function(e,t){e.count=n[t]})};function Be(e,t){if(null==e)return{};var n,r=((e,t)=>{if(null==e)return{};var n,r={};for(n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r})(e,t);if(Object.getOwnPropertySymbols)for(var l=Object.getOwnPropertySymbols(e),a=0;a<l.length;a++)n=l[a],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n]);return r}var Ue=C.createContext(void 0),B=(Ue.displayName="production"===process.env.NODE_ENV?"Anonymous":"BuilderContext",Ue.Provider),U=Ue.Consumer;function O(){return C.useContext(Ue)}function qe(t){var n,r,l,a,e,o,i,u,c,s,f,d,p,m,v=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"fields";if(null!=t&&t[v])return n=t.value,r=t.multiple,l=O(),c=A(C.useState(t[v]),2),a=c[0],e=c[1],c=A(C.useState([]),2),o=c[0],i=c[1],c=A(C.useState({options:null,parentIndex:null}),2),u=c[0],c=c[1],d=A(C.useState(null),2),s=d[0],f=d[1],d=A(C.useState(null),2),p=d[0],m=d[1],C.useEffect(function(){var e=t.ajax&&(null==(e=l.getTabFields(null==t?void 0:t.parentIndex))?void 0:e[v])||a;i(l.eligibleOptions(e)),f(l.eligibleOption(e,n,null!=r&&r))},[n,a]),C.useEffect(function(){e(t[v]),i(l.eligibleOptions(t[v]))},[t]),C.useEffect(function(){i(l.eligibleOptions(a))},[a]),C.useEffect(function(){null!=u.options&&e(u.options)},[u]),C.useEffect(function(){var e;null!=s&&(e=r?N(s)&&s.map(function(e){return e.value})||n:s.value||n,m(e))},[s]),C.useEffect(function(){var e;0===o.filter(function(e){return e.value===p}).length&&(e=F(o),m((null==e||null==(e=e[0])?void 0:e.value)||n))},[p,o]),{options:F(o),option:p,selectedOption:s,setOptions:i,setData:c};throw new Error("#options param need to set in order to use useOptions hook.")}function He(e,t,n,r){if(null!=r&&null!=(null==r?void 0:r.defaults)&&!v(r.defaults)){var l=r.defaults;if(null!=l&&!v(l)){var a={};if(null!=l&&l[n]&&k(null==l?void 0:l[n])){var o,r=l[n].indexOf("@"),i=l[n].indexOf(":");0===r&&0<i&&(r=l[n].substr(1,i-1),i=l[n].substr(i+1),o=t.getValueForDefault(r,e),""!=r)&&""!=i&&(i="false"!==i&&i,a[r]=o||i,t.setValue(r,o||i))}else if(null!=l&&l[n]&&(N(l[n])||h(l[n])))for(var u in l[n]){var c,s,f=l[n][u];f&&(N(f)||h(f))?(c=t.getValueForDefault(u,e),""!=u&&""!=f&&(f="false"!==f&&f,a[u]=c||f,t.setValue(u,c||f))):f&&(u=f.indexOf("@"),c=f.indexOf(":"),0===u)&&0<c&&(u=f.substr(1,c-1),s=f.substr(c+1),-1<f.indexOf(".")&&(u=u.split(".")),f=t.getValueForDefault(u,e),""!=u)&&""!=s&&(s="false"!==s&&s,a[u]=f||s,t.setValue(u,f||s))}return{defaultsData:a}}}}function Ge(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function ze(){return x.select("formbuilder")}function We(){return x.dispatch("formbuilder")}function r(){for(var e=arguments.length,l=new Array(e),t=0;t<e;t++)l[t]=arguments[t];return(Object.assign||function(e){for(var t=1;t<l.length;t++){var n,r=l[t];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,l)}function $e(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function i(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?$e(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):$e(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function Je(e,t){switch(t.type){case"SET_CONTEXT":return r({},e,n(e,t.payload.field,t.payload.value));case"SET_ACTIVE_TAB":return i(i({},e),{},{config:i(i({},e.config),{},{active:t.payload})});case"SET_REDIRECT":return i(i({},e),{},{redirect:i(i({},e.redirect),t.payload)});case"SET_VALUES":return r({},e,n(e,"values",t.payload));case"SET_SAVED_VALUES":return r({},e,n(e,"savedValues",t.payload));case"SET_FIELD_VALUE":return r({},e,{values:n(e.values,t.payload.field,t.payload.value)});case"SET_TOUCHED":return r({},e,{touched:t.payload});case"SET_ERRORS":return r({},e,{errors:t.payload});case"SET_STATUS":return r({},e,{status:t.payload});case"SET_ISSUBMITTING":return i(i({},e),{},{isSubmitting:t.payload});case"SET_ISVALIDATING":return r({},e,{isValidating:t.payload});case"SET_FIELD_TOUCHED":return i(i({},e),{},{touched:i(i({},e.touched),{},E({},t.payload.field,t.payload.value))});case"SET_FIELD_ERROR":case"RESET_FORM":return r({},e,t.payload);case"SUBMIT_ATTEMPT":return r({},e,{isSubmitting:!0,submitCount:e.submitCount+1});case"SUBMIT_FAILURE":case"SUBMIT_SUCCESS":return r({},e,{isSubmitting:!1});case"SET_FORM_FIELD":return null===t.payload.field?r({},e,n(e,"tabs",t.payload.value)):r({},e,{tabs:n(e.tabs,t.payload.field,t.payload.value)});case"SET_ICONS":return r({},e,{icons:n(e.icons,t.payload.name,t.payload.icons)});case"SET_ALERTS":return r({},e,{alerts:n(e.alerts,t.payload.name,t.payload.value)});case"SET_COMMONS":return r({},e,{common:n(e.common,t.payload.name,t.payload.value)});default:return e}}function Xe(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function V(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Xe(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Xe(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function R(e){var a=C.useRef(e);return nt(function(){a.current=e}),C.useCallback(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var r=arguments.length,t=new Array(r),l=0;l<r;l++)t[l]=arguments[l];return a.current.apply(void 0,t)},[])}function Ye(e){var t=p.default("wprf-row clearfix wprf-flex",null==e?void 0:e.className);return C.createElement("div",{className:t},null==e?void 0:e.children)}function Ke(e){var t=p.default("wprf-column",null==e?void 0:e.className,E(E({},"wprf-column-".concat(12/(null==e?void 0:e.column)),(null==e?void 0:e.column)&&12!==e.column),"wprf-column-12",12===e.column));return C.createElement("div",{className:t},null==e?void 0:e.children)}function Qe(e){var t=p.default("wprf-input-label",null==e?void 0:e.className);return C.createElement("label",{htmlFor:null==e?void 0:e.htmlFor,className:t},(null==e||null==(t=e.badge)?void 0:t.value)&&C.createElement("div",{className:"wprf-badge"},C.createElement("sup",{className:p.default("wprf-badge-item",{"wprf-badge-active":null==e||null==(t=e.badge)?void 0:t.active})},null==e||null==(t=e.badge)?void 0:t.label)),!(null!=e&&e.src)&&(null==e?void 0:e.children),(null==e?void 0:e.src)&&C.createElement(rt,{className:"wprf-label-image",src:e.src,alt:null==e?void 0:e.label}))}function Ze(t){var n=O(),e=t.label,r=void 0===(r=t.position)?"right":r,l=t.renderLabel,a=t.renderComponent,o=(void 0===e&&(e="Pro"),p.default("wprf-badge-item",{"wprf-badge-active":t.active})),i={};return n.is_pro_active||(i={onClick:function(e){e.preventDefault(),n.alerts.pro_alert(null==t?void 0:t.popup).fire()}}),C.createElement("div",b({className:p.default("wprf-badge-wrapper",{"pro-deactivated":!n.is_pro_active})},i),"left"===r&&0<e.length&&C.createElement(C.Fragment,null,l(C.createElement(lt,{componentClasses:o,label:e}),"left")),"right"===r&&0<e.length&&C.createElement(C.Fragment,null,l(C.createElement(lt,{componentClasses:o,label:e}),"right")),a())}function et(e){var t=e.id,n=e.label,r=e.badge,l=e.badgePosition,a=e.info,o=e.context,i=e.tooltip,e=Be(e,at);return n&&0<n.length?C.createElement("div",{className:"wprf-control-label"},"left"==l&&r,C.createElement("label",{htmlFor:t},n),i&&C.createElement("div",{className:"wprf-control-label-tooltip"},C.createElement("img",{src:null==i?void 0:i.icon}),C.createElement("div",{dangerouslySetInnerHTML:{__html:null==i?void 0:i.content}})),a&&C.createElement("div",{className:"wprf-info"},C.createElement("button",{className:"wprf-info-button"},"Info"),C.createElement("p",{className:"wprf-info-text"},C.createElement("span",{dangerouslySetInnerHTML:{__html:a}}))),(null==e?void 0:e.link)&&C.createElement("a",{rel:"nofollow",target:"_blank",href:e.link},null==o||null==(t=o.icons)?void 0:t.link),"right"==l&&r):null}function tt(e){var t=e.position,n=e.description,r=e.renderComponent,e=e.help;return C.createElement("div",{className:"wprf-control-field"},"left"===t&&n&&C.createElement("p",{className:"wprf-description",dangerouslySetInnerHTML:{__html:n}}),r(),"right"===t&&n&&C.createElement("p",{className:"wprf-description",dangerouslySetInnerHTML:{__html:n}}),e&&C.createElement("p",{className:"wprf-help",dangerouslySetInnerHTML:{__html:e}}))}var nt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?C.useLayoutEffect:C.useEffect,rt=function(e){var t;return null!=e&&e.src?(t=p.default(["wprf-input-image",null==e?void 0:e.className]),C.createElement("img",{className:t,src:null==e?void 0:e.src,alt:null==e?void 0:e.alt})):C.createElement("p",null,"No Source( src ) Defined")},lt=function(e){var t=e.componentClasses;return C.createElement("div",{className:"wprf-badge"},C.createElement("sup",{className:t},e.label))},at=["id","label","badge","badgePosition","info","context","tooltip"],ot=["label","id","name","type","style","is_pro","badge"];function it(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function u(f){return function(t){var n,r=t.label,l=t.id,e=t.name,a=t.type,o=t.style,i=t.is_pro,u=t.badge,c=Be(t,ot),s=(null==l&&(l=e),function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?it(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):it(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({description:{position:"right"}},o)),o=p.default(E(E(E({},"wprf-style-".concat(null==s?void 0:s.type),(null==s?void 0:s.type)||!1),"wprf-label-none",void 0===r||""===r||0===r.length),"wprf-".concat((null==s||null==(o=s.label)?void 0:o.position)||"inline","-label"),(null==(o=null==s||null==(o=s.label)?void 0:o.position)||o)&&null!=r));return"hidden"===a?C.createElement(f,b({},t,{id:l})):(n=P(t,["description","label","help","style"]),a=p.default("wprf-control-wrapper","wprf-type-".concat(a),o,null==t?void 0:t.classes,E({},"wprf-name-".concat(e),e)),C.createElement("div",{className:a},1==i&&C.createElement(C.Fragment,null,C.createElement(Ze,b({},u,c,{renderLabel:function(e,t){return C.createElement(et,b({},n,{context:null==c?void 0:c.context,id:l,label:r,badge:e,badgePosition:t}))},renderComponent:function(){var e;return C.createElement(tt,{help:null,description:null==t?void 0:t.description,position:null==s||null==(e=s.description)?void 0:e.position,renderComponent:function(){return C.createElement(f,b({},n,{disable:!0,id:l}))}})}})),(null==t?void 0:t.help)&&C.createElement("div",{className:"wprf-badge-wrapper"},C.createElement("div",{className:"wprf-control-label"}),C.createElement("div",{className:"wprf-control-field"},C.createElement("p",{className:"wprf-help",dangerouslySetInnerHTML:{__html:t.help}})))),(0==i||null==i)&&C.createElement(C.Fragment,null,r&&0<r.length&&C.createElement(et,b({},n,{context:null==c?void 0:c.context,label:r,id:l})),C.createElement(tt,{help:null==t?void 0:t.help,description:null==t?void 0:t.description,position:null==s||null==(o=s.description)?void 0:o.position,renderComponent:function(){return C.createElement(f,b({},n,{id:l}))}}))))}}function ut(u){var c=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return function(n){var e=O(),t=n.trigger,r=e.getFieldProps(n),l=e.getFieldMeta(r.name,n),a=e.getFieldHelpers(),o=(null!=e&&e.quickBuilder&&null!=e&&e.show&&(e.show.includes(n.name)||(r.classes=null!=r&&r.classes?r.classes+" hidden":" hidden")),null!=n&&n.parentIndex?w(n.parentIndex):[]),i=(r.parentIndex=o,r.context=e,we(n.onChange)&&(r.onChange=n.onChange),we(n.onBlur)&&(r.onBlur=n.onBlur),C.useRef({}));return C.useEffect(function(){return i.current[n.name]=!0,function(){i.current[n.name]=!1}},[]),C.useEffect(function(){var e,t;["ft_theme_three_line_one","ft_theme_three_line_two","ft_theme_four_line_two"].includes(n.name)||l.visible&&i.current[n.name]&&(c||"group"===r.type?(e=null==n?void 0:n.parent,t=null==n?void 0:n.parenttype,e&&"group"===t&&r.value&&a.setValue([e,r.name],r.value)):a.setValue(r.name,r.value))},[l.visible]),C.useEffect(function(){i.current[n.name]&&h(t)&&!v(t)&&He(r.name,a,r.value,t)},[r.value,l.visible]),l.visible?C.createElement(u,r):C.createElement(C.Fragment,null)}}function ct(e,t){var n,r,l,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return l=!(r=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return r=e.done,e},e:function(e){l=!0,n=e},f:function(){try{r||null==o.return||o.return()}finally{if(l)throw n}}};if(Array.isArray(e)||(o=((e,t)=>{var n;if(e)return"string"==typeof e?st(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?st(e,t):void 0})(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function st(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ft(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ft(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ft(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function dt(t){var e=f({type:"",label:{position:"right"},column:4},t.style),n=C.useMemo(function(){var e=!1;return null!=t&&t.checked&&h(t.checked)&&k(null==t?void 0:t.value)?e=t.checked[t.value]:k(t.value)||(e=t.value),e},[null==t?void 0:t.checked,t.value]),e=p.default("wprf-checkbox-wrap",E(E(E({},"wprf-".concat(null==e?void 0:e.type),0<(null==e?void 0:e.type.length)),"wprf-checked",Boolean(n)),"wprf-label-position-".concat(null==e||null==(n=e.label)?void 0:n.position),null==e||null==(n=e.label)?void 0:n.position),null==t?void 0:t.classes);return C.createElement("div",{className:e},C.createElement(gt,f(f({},t),{},{type:"checkbox"})),C.createElement("label",{htmlFor:t.id},t.label))}function pt(e){if(!e.type||0===e.type.length)throw console.error(e),new Error(d.__("Field must have a #type. see documentation.","notificationx"));switch(e.type){case"text":case"radio":case"email":case"range":case"number":case"hidden":return C.createElement(Et,e);case"checkbox":return C.createElement(mt,e);case"textarea":return C.createElement(Ot,e);case"codeviewer":return C.createElement(xt,e);case"message":return C.createElement(yn,e);case"select":return C.createElement(Nt,e);case"select-async":return C.createElement(on,e);case"slider":return C.createElement(an,e);case"group":return C.createElement(Pt,e);case"radio-card":return C.createElement(Yt,e);case"section":return C.createElement(Kt,e);case"date":return C.createElement(vt,e);case"toggle":return C.createElement(zt,e);case"colorpicker":return C.createElement(un,e);case"jsonuploader":return C.createElement(St,e);case"repeater":return C.createElement(ln,e);case"media":return C.createElement(sn,e);case"editor":return C.createElement(fn,e);case"action":return C.createElement(cn,e);case"button":return C.createElement(dn,e);case"modal":return C.createElement(gn,e);case"tab":return C.createElement(_n,e);case"responsive-number":return C.createElement(bn,e);default:var t=H.applyFilters("custom_field","",e.type,e);return C.createElement(C.Fragment,null,t)}}var mt=u(function(t){var n,r,l,e=t.options,a=t.value,o=t.multiple,i=t.style,u=F(e),c=f({column:4},i);return o?(e=A(C.useState({}),2),n=e[0],r=e[1],l=function(e){var t=e.target||e.currentTarget;r(function(e){return f(f({},e),{},E({},t.value,t.checked))})},C.useEffect(function(){t.onChange({target:{type:"checkbox",name:t.name,value:n,multiple:!0}})},[n]),C.useEffect(function(){if(h(a))r(a);else{var e,t={},n=ct(u);try{for(n.s();!(e=n.n()).done;)t[e.value.value]=a}catch(e){n.e(e)}finally{n.f()}r(t)}},[]),C.createElement("div",{className:"wprf-checkbox-wrapper wprf-control"},C.createElement(Ye,null,u.map(function(e){return C.createElement(Ke,{key:e.value,column:c.column},C.createElement(dt,f(f({},e),{},{context:null==t?void 0:t.context,id:e.value,checked:void 0===n[e.value]||(null!=n&&n[e.value]?a:!(null==n||!n[e.value])),type:"checkbox",onChange:l,style:c})))})))):C.createElement(gt,f(f({},t),{},{type:"checkbox"}))}),y=ut(pt,!0),g=ut(pt),vt=u(function(e){var t=e.name,n=e.value,r=e.onChange,l=e.position,a=M.__experimentalGetSettings(),o=null!=(e=null==e?void 0:e.format)?e:a.formats.datetime,i=je(n),u=/a(?!\\)/i.test(a.formats.datetime.toLowerCase().replace(/\\\\/g,"").split("").reverse().join(""));return C.useEffect(function(){r({target:{type:"date",name:t,value:i}})},[]),C.createElement(m.Dropdown,{className:"wprf-control-datetime",contentClassName:"wprf-control-datetime-content",position:l||"bottom right",renderToggle:function(e){return e.isOpen,C.createElement(m.Button,{isTertiary:!0,onClick:e.onToggle},M.date(o,i,-(new Date).getTimezoneOffset()))},renderContent:function(){return C.createElement(m.DateTimePicker,{__nextRemoveHelpButton:!0,__nextRemoveResetButton:!0,currentDate:je(i).toDate().toString(),onChange:function(e){r({target:{type:"date",name:t,value:le.default(e).utc().format()}})},is12Hour:u})}})});function bt(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function j(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?bt(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):bt(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function yt(t,e){function n(e){return l.onChange(e,{popup:null==t?void 0:t.popup,isPro:!!t.is_pro,nx_has_permission:!!t.nx_has_permission,permission_popup:null==t?void 0:t.nx_has_permission})}var r=t.type||"text",l=P(j(j({},t),{},{type:r}),["is_pro","nx_has_permission","visible","trigger","copyOnClick","disable","parentIndex","context","badge","popup","tags"]),r=C.useRef(null),e=null!=e&&e.current?e:r,a=O();"checkbox"===l.type&&null!=l&&l.name&&(l.checked=(null==l?void 0:l.checked)||(null==l?void 0:l.value));var o,i=(r=A(C.useState(!1),2))[0],u=r[1],c=(C.useEffect(function(){var e;return i&&(e=setTimeout(function(){u(!1)},2e3)),function(){return e&&clearTimeout(e)}},[i]),C.useCallback(function(e){e=null==e||null==(e=e.target)?void 0:e.getAttribute("data-num-sug");a.setFieldValue(l.name,e)},[l]));return!t.is_pro&&null!=t&&t.copyOnClick&&null!=t&&t.value?(r=(null==t?void 0:t.copyMessage)||"Click To Copy!",o=(null==t?void 0:t.copiedMessage)||"Copied!",C.createElement("span",{className:"wprf-clipboard-wrapper"},s.default.createElement("input",j(j({},l),{},{onChange:n})),C.createElement("span",{className:"wprf-clipboard-tooltip"},C.createElement("span",{className:"wprf-clipboard-tooltip-text"},i?o:r),C.createElement(m.Button,{className:"wprf-copy-icon",onClick:function(){ie.default(t.value,{format:"text/plain",onCopy:function(){u(!0)}})}},"Copy")))):C.createElement("span",null,s.default.createElement("input",j(j({},l),{},{onChange:n,ref:e})),(null==l?void 0:l.suggestions)&&0<(null==l?void 0:l.suggestions.length)&&C.createElement("div",{className:"wprf-num-suggestions"},null==l||null==(o=l.suggestions)?void 0:o.map(function(e,t){return C.createElement("span",{onClick:c,"data-num-sug":e.value},e.value+" "+e.unit)})))}var gt=s.default.memo(s.default.forwardRef(yt)),Et=u(s.default.memo(yt));function wt(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function ht(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?wt(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):wt(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Ot=u(s.default.memo(function(t){var n=P(t,["is_pro","visible","trigger","disable","parentIndex","context"]),e=C.useCallback(function(e){return n.onChange(e,{isPro:!!t.is_pro})},[null==n?void 0:n.value]);return s.default.createElement("textarea",ht(ht({},n),{},{onChange:e,rows:5}))}));function jt(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _t(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?jt(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):jt(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var xt=u(s.default.memo(function(t){var n=P(t,["is_pro","visible","trigger","disable","parentIndex","context","copyOnClick"]),e={onChange:C.useCallback(function(e){return n.onChange(e,{isPro:!!t.is_pro})},[null==n?void 0:n.value]),rows:5},r=(!t.is_pro&&null!=t&&t.copyOnClick&&null!=t&&t.value&&(e.onClick=function(){var e=null!=t&&t.success_text?t.success_text:d.__("Copied to Clipboard.","notificationx");ie.default(t.value,{format:"text/plain",onCopy:function(){t.context.alerts.toast("success",e)}})}),null!=t&&t.button_text?t.button_text:d.__("Click to Copy","notificationx"));return C.createElement("span",{className:"wprf-code-viewer"},s.default.createElement("textarea",_t(_t({},n),e)),C.createElement(m.Button,{className:"wprf-copy-button"},r))})),St=u(s.default.memo(function(n){P(n,["is_pro","visible","trigger","disable","parentIndex","context","copyOnClick"]);var e=A(C.useState(),2),t=e[0],r=e[1];return C.useEffect(function(){null!=n&&n.value||r(null)},[null==n?void 0:n.value]),C.createElement("span",{className:"wprf-json-uploader"},!t&&C.createElement("label",{className:"wprf-json-uploaderButton"},C.createElement("span",null,d.__("Upload")),C.createElement("input",{type:"file",accept:"application/JSON",onChange:function(e){var t;(e=e).target.files.length&&(0==(null==(e=e.target.files[0])?void 0:e.size)?n.context.alerts.toast("error",d.__("File can't be empty.","notificationx")):"application/json"!=(null==e?void 0:e.type)&&"text/json"!=(null==e?void 0:e.type)?n.context.alerts.toast("error",d.__("Invalid file type.","notificationx")):(r(e),(t=new FileReader).onload=function(e){e=null==e||null==(e=e.target)?void 0:e.result;n.onChange({target:{type:"jsonuploader",name:n.name,value:e}})},t.readAsText(e)))}})),t&&(null==t?void 0:t.name)&&C.createElement("span",{className:"wpfr-json-file-name-wrapper"},C.createElement("span",{className:"wpfr-json-file-name"},20<(null==t?void 0:t.name.length)?"".concat(null==t?void 0:t.name.substr(0,9),"...").concat(null==t?void 0:t.name.substr((null==t?void 0:t.name.length)-7)):null==t?void 0:t.name),C.createElement("span",{className:"wprf-json-file-delete-button",onClick:function(){r(null),n.onChange({target:{type:"jsonuploader",name:n.name,value:null}})}},"x")))})),Ct=["name","fields"],Pt=u(function(n){var r,l,e,t,a=n.name,o=n.fields,i=Be(n,Ct);if(o&&N(o)&&0!==o.length)return r=O(),l=C.useCallback(function(e){e.persist&&e.persist();var e=he(e),t=e.field;r.setFieldValue([a,t],e.val)},[n.value]),e=F(o),C.useEffect(function(){r.setFormField([].concat(w(n.parentIndex),["fields"]),e)},[]),o=e.map(function(e,t){t=[].concat(w(n.parentIndex),["fields",t]);return C.createElement(y,b({},i,{key:e.name,index:n.index,onChange:l},e,{parenttype:"group",parent:a,parentIndex:t}))}),t=p.default("wprf-group-control-inner",{"wprf-display-inline":"inline"===(null==n?void 0:n.display)}),C.createElement("div",{className:"wprf-group-control"},C.createElement("div",{className:t},o));throw new Error(d.__("You should give a #fields arguments to a group field.","notificationx"))});function kt(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}var Nt=u(function(l){function e(){if(l.ajax&&(!l.ajax.rules||I(l.ajax.rules,a.values))){y(!0);var r={};if(Object.keys(null==l?void 0:l.ajax.data).map(function(e){var t,n;-1<(null==l?void 0:l.ajax.data[e].indexOf("@"))?(n=null==l?void 0:l.ajax.data[e].substr(1),r[e]=null==(t=a.values)?void 0:t[n]):r[e]=null==l?void 0:l.ajax.data[e]}),!g)return Ce({path:null==l?void 0:l.ajax.api,data:r}).then(function(e){y(!1);var t=_e(l.options,e,"value");return a.setFormField([].concat(w(c),["options"]),t),p({options:t,parentIndex:[].concat(w(c),["options"])}),e})}}var a=O(),t=l.id,n=l.name,r=l.multiple,o=l.placeholder,i=l.search,i=void 0!==i&&i,u=l.onChange,c=l.parentIndex,s=qe(l,"options"),f=s.options,d=s.selectedOption,p=s.setData,s=A(C.useState(null),2),m=s[0],v=s[1],s=A(C.useState(!1),2),b=s[0],y=s[1],s=A(C.useState(!1),2),g=s[0];return C.useEffect(function(){!N(m)&&h(m)&&u({target:{type:"select",name:n,value:m.value,options:f,multiple:r}}),N(m)&&u({target:{type:"select",name:n,value:m.map(function(e){return e.value}),options:f,multiple:r}})},[m]),C.useEffect(function(){e()},[]),C.useEffect(function(){null!=l&&l.menuOpen&&e()},[null==l?void 0:l.menuOpen]),C.createElement("div",{className:"wprf-select-wrapper"},C.createElement(ue.default,{isDisabled:null==l?void 0:l.disable,classNamePrefix:"wprf-select",isSearchable:null!=i&&i,id:t,name:n,isMulti:null!=r&&r,placeholder:o,isLoading:b,options:f,value:d,onMenuOpen:e,onMenuClose:function(){y(!1)},isOptionDisabled:function(e){return null==e?void 0:e.disabled},onChange:function(e){!N(e)&&h(e)?v(function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?kt(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):kt(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({},e)):N(e)?v(w(e)):v(e)}}))});let Dt=new WeakMap;function Tt(n,r,l){return C.useMemo(()=>{var e,t;return l||(e=n,t=Dt.get(e)||0,Dt.set(e,t+1),r?r+"-"+t:t)},[n])}var Ft=function(t){var e=O(),n=t.fields,r=t.onChange,l=t.index,a=t.parent,o=A(C.useState(t.isCollapsed),2),i=o[0],u=o[1],c=Tt(Ft),o=null==(o=e.values)||null==(o=o[a])?void 0:o[l],o=(null==o?void 0:o.title)||(null==o?void 0:o.post_title)||(null==o?void 0:o.username)||(null==o?void 0:o.plugin_theme_name),o=o?o.length<40?o:o.substr(0,40)+"...":"";return C.useEffect(function(){e.setFieldValue([a,l,"isCollapsed"],i)},[i]),C.createElement("div",{className:"wprf-repeater-field"},C.createElement("div",{className:"wprf-repeater-field-title",onClick:function(){return u(!i)}},C.createElement("h4",null,C.createElement(m.Icon,{icon:"move"}),t.index+1,": ",o),C.createElement("div",{className:"wprf-repeater-field-controls"},C.createElement(m.Icon,{onClick:function(e){null!=e&&e.stopPropagation(),t.clone(t.index)},icon:"admin-page"}),C.createElement(m.Icon,{onClick:function(e){null!=e&&e.stopPropagation(),t.remove(t.index)},icon:"trash"}))),!i&&C.createElement("div",{className:"wprf-repeater-inner-field"},n.map(function(e,t){return C.createElement(y,b({key:"field-".concat(l,"-").concat(t)},e,{id:"field-".concat(c,"-").concat(l,"-").concat(t),index:l,parenttype:"repeater",parent:a,onChange:function(e){return r(e,l)}}))})))};function It(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function At(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?It(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):It(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function Vt(n){var e=n.isLoading,t=n.closeModal,r=A(C.useState([]),2),l=r[0],a=r[1];return C.useEffect(function(){var e=F(n.body.fields).map(function(e,t){t=[].concat(w(n.parentIndex),["fields",t]);return C.createElement(g,b({key:e.name},e,{parentIndex:t}))});a(e)},[]),C.createElement("div",{className:"wprf-modal-body"},e&&C.createElement(Mt,null),!e&&C.createElement(C.Fragment,null,C.createElement("div",{className:"wprf-modal-content"},0<l.length&&l),C.createElement("div",{className:"wprf-modal-footer clearfix"},C.createElement("div",{className:"wprf-modal-footer-left"},(null==(r=n.body)?void 0:r.footer)&&k(n.body.footer)&&C.createElement("p",null,n.body.footer),null==n||!n.confirm_button||null!=n&&null!=(e=n.confirm_button)&&e.close_action?"":C.createElement(y,b({type:"button"},n.confirm_button)),null!=n&&n.confirm_button&&null!=n&&null!=(l=n.confirm_button)&&l.close_action?C.createElement(y,{type:"button",onClick:t,text:null==n||null==(r=n.confirm_button)?void 0:r.text}):""))))}function Rt(e){return e=e.content,C.createElement("div",{className:"wprf-modal-header"},e&&k(e)&&C.createElement("h3",null,e))}var Lt=u(function(t){var e=At({type:"",label:{position:"right"},column:4},t.style),n=C.useMemo(function(){var e=!1;return null!=t&&t.checked&&h(t.checked)&&k(null==t?void 0:t.value)?e=t.checked[t.value]:k(t.value)||(e=t.value),e},[null==t?void 0:t.checked,t.value]),e=p.default("wprf-toggle-wrap",E(E(E({},"wprf-".concat(null==e?void 0:e.type),0<(null==e?void 0:e.type.length)),"wprf-checked",Boolean(n)),"wprf-label-position-".concat(null==e||null==(n=e.label)?void 0:n.position),null==e||null==(n=e.label)?void 0:n.position),null==t?void 0:t.classes);return C.createElement("div",{className:e},C.createElement(gt,At(At({},t),{},{type:"checkbox",placeholder:void 0})),C.createElement(Qe,{htmlFor:t.id}))}),Mt=function(e){return C.createElement("p",null,d.__("Loading...","notificationx"))},Bt={options:["inline","blockType","textAlign","colorPicker","link"],inline:{options:["bold","italic","underline","strikethrough","monospace"]},blockType:{inDropdown:!0,options:["Normal","H1","H2","H3","H4","H5","H6","Blockquote","Code"],className:void 0,component:void 0,dropdownClassName:void 0}};function Ut(e,t){var n,r,l,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return l=!(r=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return r=e.done,e},e:function(e){l=!0,n=e},f:function(){try{r||null==o.return||o.return()}finally{if(l)throw n}}};if(Array.isArray(e)||(o=((e,t)=>{var n;if(e)return"string"==typeof e?qt(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?qt(e,t):void 0})(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function qt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Ht(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function Gt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ht(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ht(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var zt=function(t){var n,r,l,e=t.options,a=t.value,o=t.multiple,i=t.style,u=F(e),c=Gt({column:4},i);return o?(e=A(C.useState({}),2),n=e[0],r=e[1],l=function(e){var t=e.target||e.currentTarget;r(function(e){return Gt(Gt({},e),{},E({},t.value,t.checked))})},C.useEffect(function(){t.onChange({target:{type:"toggle",name:t.name,value:n}})},[n]),C.useEffect(function(){if(h(a))r(a);else{var e,t={},n=Ut(u);try{for(n.s();!(e=n.n()).done;)t[e.value.value]=a}catch(e){n.e(e)}finally{n.f()}r(t)}},[]),C.createElement("div",{className:"wprf-toggle-wrapper wprf-control"},C.createElement(Ye,null,u.map(function(e){return C.createElement(Ke,{key:e.value,column:c.column},C.createElement(Lt,Gt(Gt({},e),{},{context:null==t?void 0:t.context,id:e.value,checked:void 0===n[e.value]||(null!=n&&n[e.value]?a:!(null==n||!n[e.value])),type:"checkbox",onChange:l,style:c})))})))):C.createElement(Lt,t)},Wt=["label","value","icon","is_pro"];function $t(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function Jt(e){var i,u,c,s=O(),t=qe(e,"options"),n=t.options,f=t.option;if(n)return i=Tt(Jt),t=p.default(["wprf-control","wprf-radio-card","wprf-input-radio-set-wrap",null==e?void 0:e.className]),u=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?$t(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):$t(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({},null==e?void 0:e.style),c=P(e,["options","placeholder","style","trigger"]),C.useEffect(function(){f&&e.onChange({target:{type:"radio-card",name:e.name,value:f}})},[f]),C.createElement("div",{className:t},C.createElement(Ye,null,n.map(function(e,t){var n,r=e.label,l=e.value,a=e.icon,o=e.is_pro,e=Be(e,Wt);return C.createElement(Ke,{column:+(null==e?void 0:e.column)||4,key:t},C.createElement("div",{className:p.default("wprf-input-radio-option",{"wprf-option-has-image":null!=a&&a,"wprf-option-selected":l==f})},C.createElement(Qe,{className:p.default(E({"wprf-label-has-image":null!=a&&a},"wprf-size-".concat(u.size),null!=(n=a&&(null==u?void 0:u.size))&&n)),htmlFor:"wprf-input-radio-".concat(i,"-").concat(t),src:a,badge:{label:o?"Pro":"Free",value:o,active:Boolean(s.is_pro_active)}},r),C.createElement(gt,b({},e,c,{is_pro:o,type:"radio",value:l,checked:l===f,id:"wprf-input-radio-".concat(i,"-").concat(t)}))))})));throw new Error(d.__("#options is a required arguments for RadioCard field.","notificationx"))}var Xt,Yt=u(Jt),Kt=s.default.memo(function(n){var t=O(),e=A(C.useState(null!=(e=n.collapsed)&&e),2),r=e[0],l=e[1],e=A(C.useState([]),2),a=e[0],o=e[1],e=(C.useEffect(function(){var e=F(n.fields),e=(t.setFormField([].concat(w(n.parentIndex),["fields"]),e),e.map(function(e,t){t=[].concat(w(n.parentIndex),["fields",t]);return C.createElement(g,b({key:e.name},e,{parentIndex:t}))}));o(e)},[]),p.default("wprf-control-section",null==n?void 0:n.classes,null==n?void 0:n.name,{"wprf-section-collapsed":(null==n?void 0:n.collapsible)&&r}));return C.createElement("div",{id:null==n?void 0:n.name,className:e},n.placeholder&&C.createElement("div",{className:"wprf-section-title"},C.createElement("h4",null,n.placeholder),n.collapsible&&C.createElement("button",{onClick:function(){return l(!r)}},"Icon")),C.createElement("div",{className:"wprf-section-fields"},a))}),Qt=new Uint8Array(16);var Zt=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var c=[],en=0;en<256;++en)c.push((en+256).toString(16).substr(1));function tn(e,t){var t=1<arguments.length&&void 0!==t?t:0,e=(c[e[t+0]]+c[e[t+1]]+c[e[t+2]]+c[e[t+3]]+"-"+c[e[t+4]]+c[e[t+5]]+"-"+c[e[t+6]]+c[e[t+7]]+"-"+c[e[t+8]]+c[e[t+9]]+"-"+c[e[t+10]]+c[e[t+11]]+c[e[t+12]]+c[e[t+13]]+c[e[t+14]]+c[e[t+15]]).toLowerCase();if("string"==typeof(t=e)&&Zt.test(t))return e;throw TypeError("Stringified UUID is invalid")}function nn(e,t,n){var r=(e=e||{}).random||(e.rng||function(){if(Xt=Xt||"undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto))return Xt(Qt);throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported")})();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var l=0;l<16;++l)t[n+l]=r[l];return t}return tn(r)}function rn(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?rn(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):rn(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var ln=function(e){var l=e.name,t=e.button,n=e.fields,a=O(),e=A(C.useState(null==(e=a.values)?void 0:e[l]),2),r=e[0],o=e[1],i=(C.useEffect(function(){var e;null!=(null==(e=a.values)?void 0:e[l])&&o(null==(e=a.values)?void 0:e[l])},[null==(e=a.values)?void 0:e[l]]),C.useCallback(function(e){var t=w(r);t.splice(e,1),a.setFieldValue(l,t)},[r])),u=C.useCallback(function(e){var t=w(r);0<t.length&&(null!=(e=t=null!=(e=t=null!=(e=t=null!=(e=t=(null==t?void 0:t[e])||{})&&e.title?_(_({},t),{},{title:t.title+" - Copy"}):t)&&e.post_title?_(_({},t),{},{post_title:t.post_title+" - Copy"}):t)&&e.username?_(_({},t),{},{username:t.username+" - Copy"}):t)&&e.plugin_theme_name&&(t=_(_({},t),{},{plugin_theme_name:t.plugin_theme_name+" - Copy"})),t=_(_({},t),{},{index:nn(),isCollapsed:!1}),a.setFieldValue([l,r.length],t))},[r]);return C.useEffect(function(){o(null==r||""==r?[{index:nn()}]:function(e){return e.map(function(e){return _(_({},e),{},{index:nn()})})})},[]),C.createElement("div",{className:"wprf-repeater-control"},r&&0<(null==r?void 0:r.length)&&C.createElement($.ReactSortable,{className:"wprf-repeater-content",list:r,setList:function(e){a.setFieldValue(l,e)},handle:".wprf-repeater-field-title",filter:".wprf-repeater-field-controls",forceFallback:!0},r.map(function(e,r){return C.createElement(Ft,{isCollapsed:null==e?void 0:e.isCollapsed,key:(null==e?void 0:e.index)||r,fields:n,index:r,parent:l,clone:u,remove:i,onChange:function(e){var t,n;t=r,(e=e).persist&&e.persist(),e=he(e),n=e.field,a.setFieldValue([l,t,n],e.val)}})})),C.createElement("div",{className:"wprf-repeater-label"},C.createElement("button",{className:"wprf-repeater-button",onClick:function(){return a.setFieldValue(l,[].concat(w(r),[{index:nn()}]))}},null==t?void 0:t.label)))},an=function(t){var n=t.name,e=t.id,r=t.label,l=t.units,a=t.min,o=t.max,i=t.unit,u=t.reset,c=A(C.useState(t.value||0),2),s=c[0],f=c[1],c=A(C.useState(i),2),d=c[0],p=c[1];return C.useEffect(function(){var e;s&&(Ee(s)?e=d?"".concat(s).concat(d):"".concat(s):k(s)&&(e=-1<s.indexOf(d)?"".concat(s):"".concat(s).concat(d)),t.onChange({target:{type:"slider",name:n,value:e}}))},[s,d]),C.createElement("div",{className:"wprf-slider-wrap"},C.createElement("div",{className:"wprf-slider-control-head"},C.createElement(Qe,{htmlFor:e||n},r),N(l)&&0<l.length&&C.createElement("div",{className:"wprf-slider-units"},l.map(function(e,t){return C.createElement(m.Button,{key:t,isSmall:!0,isPrimary:!0,onClick:function(){return p(e)},className:e==d?"unit-active":""},e)}))),C.createElement("div",{className:"wprf-slider-control"},C.createElement(m.RangeControl,{allowReset:null==u||u,value:parseInt(s),min:a,max:o,onChange:function(e){return f(e)}})))},on=u(function(l){function a(t,n){var e;if(l.ajax&&(!l.ajax.rules||I(l.ajax.rules,o.values)))if(t)if(t.length<3)n([{label:"Please type 3 or more characters.",value:null,disabled:!0}]);else{var r={inputValue:t};if(null!=(e=Object.keys(l.ajax.data))&&e.map(function(e){var t,n;-1<l.ajax.data[e].indexOf("@")?(n=l.ajax.data[e].substr(1),r[e]=null==(t=o.values)?void 0:t[n]):r[e]=l.ajax.data[e]}),!p&&t)return m(!0),window.lastRequest=null,Ce({path:l.ajax.api,data:r}).then(function(e){return n(e),e}).finally(function(){var e;m(!1),window.lastRequest&&(e=window.lastRequest,window.lastRequest=null,a.apply(void 0,w(e))),window.lastCompleteRequest=t});window.lastRequest=[t,n]}else n(c)}var o=O(),e=l.id,t=l.name,n=l.multiple,r=l.placeholder,i=l.onChange,u=A(C.useState(o.eligibleOptions(l.options)),2),c=u[0],s=u[1],u=A(C.useState(null==l?void 0:l.value),2),f=u[0],d=u[1],u=A(C.useState(!1),2),p=u[0],m=u[1];return C.useEffect(function(){s(o.eligibleOptions(l.options))},[o.values.source]),C.useEffect(function(){i({target:{type:"select",name:t,value:f,multiple:n}})},[f]),C.createElement("div",{className:"wprf-async-select-wrapper"},C.createElement(se.default,{cacheOptions:!0,loadOptions:a,defaultOptions:c,isDisabled:null==l?void 0:l.disable,isMulti:null!=n&&n,classNamePrefix:"wprf-async-select",id:e,name:t,placeholder:r,formatOptionLabel:function(e,t){var n,r;if(null!=t&&null!=(n=t.inputValue)&&n.length&&e.name&&e.name.toLowerCase().includes(null==t||null==(n=t.inputValue)?void 0:n.toLowerCase()))return null!=e&&e.name,n=new RegExp("(".concat(null==t?void 0:t.inputValue,")"),"gi"),t=null==(t=e.name)?void 0:t.replace(n,"<strong style={font-weight: 900}>$1</strong>"),r=null==(r=e.address)?void 0:r.replace(n,"<strong style={font-weight: 900}>$1</strong>"),C.createElement(C.Fragment,null,ce.default(t||"")," ",C.createElement("small",null,ce.default(r||"")));return C.createElement(C.Fragment,null,e.name?C.createElement(C.Fragment,null,C.createElement("b",null,e.name)," "):C.createElement(C.Fragment,null,e.label," "),e.address&&C.createElement("small",null,e.address))},value:f,isClearable:!0,isOptionDisabled:function(e){return null==e?void 0:e.disabled},onChange:function(e){return d(e)}}))}),un=u(function(e){var t,n=e.value,r=e.name,l=e.id,a=e.onChange,e=A(C.useState(!1),2),o=e[0],i=e[1],e=A(C.useState(n||null),2),u=e[0],c=e[1],e=A(C.useState(null),2),s=e[0],f=e[1],e=C.useRef(null);C.useEffect(function(){f(n)},[]);return C.useEffect(function(){a({target:{type:"colorpicker",name:r,value:u}})},[u]),t=e,C.useEffect(function(){function e(e){t.current&&!t.current.contains(e.target)&&i(!1)}return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}},[t]),C.createElement(C.Fragment,null,C.createElement("div",{className:"wprf-colorpicker-wrap",ref:e},C.createElement("input",{type:"hidden",value:n,name:r,id:l}),C.createElement("span",{className:"wprf-picker-display",style:{backgroundColor:n,borderColor:n},onClick:function(){return i(!o)}}),o&&C.createElement(C.Fragment,null,C.createElement("button",{className:"wprf-colorpicker-reset",onClick:function(e){e.preventDefault(),c(s),i(!1)}},d.__("Reset","notificationx")),C.createElement(m.ColorPicker,{color:n,onChangeComplete:function(e){return c(e.hex)}}))))}),cn=function(e){return C.createElement(C.Fragment,null,H.applyFilters(e.action,"",e))},sn=u(function(n){var e=A(C.useState(null!=(e=n.value)&&e.url?n.value:null),2),r=e[0],l=e[1],a=O();return C.useEffect(function(){n.onChange({target:{type:"media",name:n.name,value:r}})},[r]),C.createElement("div",{className:"wprf-control wprf-media"},null!=r&&!(null!=n&&n.notImage)&&C.createElement("div",{className:"wprf-image-preview"},null!=r&&(null==r?void 0:r.url)&&C.createElement("img",{src:r.url,alt:r.title})),C.createElement("div",{className:"wprf-image-uploader"},C.createElement(Y.MediaUpload,{onSelect:function(e){l({id:e.id,title:e.title,url:e.url})},multiple:!1,allowedTypes:["image"],value:r,render:function(e){var t=e.open;return C.createElement(C.Fragment,null,null!=r&&C.createElement("button",{className:"wprf-btn wprf-image-remove-btn",onClick:function(){null!=n&&n.is_pro&&!a.is_pro_active?a.alerts.pro_alert(null==n?void 0:n.popup).fire():l(null)}},(null==n?void 0:n.remove)||"Remove"),C.createElement("button",{className:"wprf-btn wprf-image-upload-btn",onClick:function(){null!=n&&n.is_pro&&!a.is_pro_active?a.alerts.pro_alert(null==n?void 0:n.popup).fire():t()}},null!=r?(null==n?void 0:n.reset)||"Change Image":(null==n?void 0:n.button)||"Upload"))}})))}),fn=u(function(n){var e=A(C.useState(Q.EditorState.createEmpty()),2),t=e[0],r=e[1];return C.useEffect(function(){var e,t;n.value&&(e=(t=de.default(n.value)).contentBlocks,e=Q.ContentState.createFromBlockArray(e,t.entityMap),t=Q.EditorState.createWithContent(e),r(t))},[]),C.useEffect(function(){var e=fe.default(Q.convertToRaw(t.getCurrentContent()));n.onChange({target:{type:"editor",value:e,name:n.name}})},[t]),C.createElement(K.Editor,{placeholder:null==n?void 0:n.placeholder,toolbar:Bt,editorState:t,toolbarClassName:"wprf-editor-toolbar",wrapperClassName:"wprf-editor wprf-control",editorClassName:"wprf-editor-main",onEditorStateChange:r})}),dn=u(function(n){var e,t,r,l;if(null!=n&&n.text||!0===(null==n?void 0:n.group))return e=P(n,["is_pro","visible","disable","parentIndex","context","onBlur","value","ajax","text"]),t=(l=A(C.useState(!1),2))[0],r=l[1],null!=n&&n.href?C.createElement("a",{href:-1===(null==n?void 0:n.href)?null==n?void 0:n.value:null==n?void 0:n.href,target:null==n?void 0:n.target,className:p.default("wprf-control wprf-button wprf-href-btn",null==n?void 0:n.classes)},null==n?void 0:n.text):null!=n&&n.group?(l=n.fields.map(function(e,t){t=[].concat(w(n.parentIndex),["fields",t]);return C.createElement(g,b({key:e.name},e,{parentIndex:t}))}),C.createElement("div",{className:"wprf-control wprf-button-group wprf-flex"},l)):C.createElement(C.Fragment,null,C.createElement("button",b({},e,{name:n.name,disabled:t,onClick:null!=(l=null==n?void 0:n.onClick)?l:function(e){var t,l;null!=n&&n.ajax&&(r(!0),Oe(n.ajax,n.context).then(function(e){var t;if(r(!1),"error"==(null==e?void 0:e.status))throw new Error(null==e?void 0:e.message);n.onChange({target:{type:"button",name:n.name,value:!0}}),null!=(e=n.ajax)&&e.hideSwal||(e=(null==(e=n.ajax)||null==(e=e.swal)?void 0:e.icon)||"success",t=(null==(t=n.ajax)||null==(t=t.swal)?void 0:t.text)||"Complete",n.context.alerts.toast(e,t,{autoClose:null==(e=n.ajax)||null==(e=e.swal)?void 0:e.autoClose})),null!=(t=n.ajax)&&t.reload&&setTimeout(function(){return window.location.reload()},1e3)}).catch(function(e){var t;console.error("Error In Button Called",n.name,e),r(!1),n.onChange({target:{type:"button",name:n.name,value:!1}}),null!=(t=n.ajax)&&t.hideSwal||n.context.alerts.toast("error",(null==e?void 0:e.message)||d.__("Something went wrong.","notificationx"))})),l=(t=n).context,null!=t&&t.trigger&&N(null==t?void 0:t.trigger)&&null!=t&&t.trigger.map(function(e){var t=(null==e?void 0:e.type)||"setFieldValue";if(null!=e&&e.action&&h(null==e?void 0:e.action))for(var n in null==e?void 0:e.action){var r=n,n=(-1<r.indexOf(".")&&(r=r.split(".")),null==e?void 0:e.action[n]);""!=r&&l[t](r,n)}})},className:p.default("wprf-control wprf-button wprf-btn",null==n?void 0:n.classes)}),(null==n?void 0:n.icon)&&(h(n.icon)?null==n||null==(e=n.context)||null==(e=e.icons)||null==(e=e[null==n||null==(l=n.icon)?void 0:l.type])?void 0:e[null==n||null==(l=n.icon)?void 0:l.name]:""),h(null==n?void 0:n.text)&&null!=n&&n.ajax?t?null==n||null==(e=n.text)?void 0:e.loading:n.value?null==n||null==(l=n.text)?void 0:l.saved:null==n||null==(t=n.text)?void 0:t.normal:null==n?void 0:n.text));throw new Error(d.__("Button has a required params #text.","notificationx"))});function pn(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function mn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?pn(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):pn(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function vn(t){var n=P(t,["is_pro","visible","trigger","disable","parentIndex","context","badge","popup"]),r=(e=A(C.useState(Object.keys(t.controls)[0]),2))[0],l=e[1],e=n.value;h(n.value)||Object.keys(t.controls).reduce(function(e,t){return mn(mn({},e),{},E({},t,n.value))},{});var a=(e=A(C.useState(e),2))[0],o=e[1];return C.useEffect(function(){n.onChange({target:{type:"input",name:n.name,value:a,checked:null,multiple:null}})},[a]),C.createElement("div",{style:{display:"flex",alignItems:"center",rowGap:5,columnGap:10,flexWrap:"wrap"}},s.default.createElement("input",mn(mn({},n),{},{type:"number",value:null==a?void 0:a[r],onChange:function(e){o(mn(mn({},a),{},E({},r,e.target.value)))}})),C.createElement("div",{style:{display:"flex",alignItems:"center"}},null==(e=Object.keys(t.controls))?void 0:e.map(function(e){return C.createElement("button",{type:"button",key:e,className:"responsive-button ".concat(r===e?"active":""),onClick:function(){return l(e)}},C.createElement("img",{src:t.controls[e].icon,alt:"desktop",style:{width:t.controls[e].size}}))})))}vn.defaultProps={type:"number"};var bn=u(s.default.memo(vn)),yn=function(e){var t=(e=>{if(null!=e&&e.messages)for(var t in e.messages){t=e.messages[t];if(I(t.rules,e.context.values))return t}return{message:null==e?void 0:e.message,html:null==e?void 0:e.html,type:"normal"}})(e),n=t.html,r=t.message,t=t.type;return r?C.createElement("div",{className:p.default("wprf-control","wprf-message","wprf-".concat(void 0===t?"warning":t,"-message"),"wprf-".concat(e.name,"-message"),null==e?void 0:e.classes)},n&&C.createElement("p",{dangerouslySetInnerHTML:{__html:r}}),!n&&C.createElement("p",null,r)):C.createElement(C.Fragment,null)},gn=function(n){var e;if(null==(null==n?void 0:n.body)||null==(null==n?void 0:n.button))throw new Error(d.__("Modal needs button/body with it.","notificationx"));function r(){return i(!0)}function t(){return i(!1)}function l(){var e=null==(e=n.context.values)?void 0:e[n.cancel];null!=n&&n.cancel&&e&&e!==s&&t()}var a=A(C.useState(!1),2),o=a[0],i=a[1],a=A(C.useState(!1),2),a=a[0],u=C.useCallback(function(){},[]),c=C.useRef(),s=(C.useEffect(function(){var e;c.current=null==(e=n.context.values)?void 0:e[n.cancel]}),c.current);return C.createElement("div",{className:"wprf-control wprf-modal",id:"wprf-modal-".concat(n.name)},!(null!=n&&n.close_on_body)&&C.createElement(y,b({type:"button"},null==n?void 0:n.button,{onClick:r})),(null==n?void 0:n.show_body)&&C.createElement("div",{className:"wprf-control wprf-modal-show-body"},null==n||null==(e=n.body)||null==(e=e.fields)?void 0:e.map(function(e){var t;return"text"===e.type?C.createElement("div",{className:"wprf-control wprf-modal-body-value-heading"},C.createElement("h4",{key:e.name},(null==(t=n.context.values)?void 0:t[e.name])||(null==e?void 0:e.default)),(null==n?void 0:n.close_on_body)&&C.createElement(y,b({type:"button"},null==n?void 0:n.button,{onClick:r}))):"textarea"===e.type?C.createElement("p",{key:e.name},(null==(t=n.context.values)?void 0:t[e.name])||(null==e?void 0:e.default)):null})),o&&C.createElement(pe.default,{customClass:"wprf-modal-inner",style:{maxWidth:"900px",width:"100%",overflowY:"scroll",margin:"50px auto"},closeBtnStyle:{top:"5px",right:"5px",color:"#f78c8c",fontSize:"18px",border:"1px solid #f78c8c",borderRadius:"50%",width:"30px",height:"30px",display:"inline-flex",alignItems:"center",justifyContent:"center"},title:C.createElement(Rt,{content:null==n||null==(e=n.body)?void 0:e.header}),onConfirm:u,showConfirm:!1,showCloseButton:!0,closeOnClickOutside:!0,onCancel:t,afterUpdate:function(){return l}},C.createElement(Vt,b({},n,{isLoading:a,closeModal:t,context:n.context,onConfirm:u}))))},En=function(e){var t=e.fields,r=e.parentIndex,n=e.context,e=A(C.useState([]),2),l=e[0],a=e[1],e=A(C.useState([]),2),o=e[0],i=e[1];return C.useEffect(function(){var e=F(t);n.setFormField([r,"fields"],e),a(e)},[]),C.useEffect(function(){var e;N(l)&&0<l.length&&(e=l.map(function(e,t){var n=[].concat(w(r),["fields",t]);return"section"===(null==e?void 0:e.type)?C.createElement(y,b({key:"input-".concat(e.name,"-").concat(t)},e,{parentIndex:n})):e?C.createElement(g,b({key:"input-".concat(e.name,"-").concat(t)},e,{parentIndex:n})):C.createElement(C.Fragment,null)}),i(e))},[l]),C.createElement(C.Fragment,null,o)};function wn(e){var t=e.fields,r=e.active,n=e.setActive,l=e.submit,a=Be(e,jn);if(void 0===t)throw new Error(d.__("There are no #tabs args defined in props.","notificationx"));var o,i,u=O(),c=a.parentIndex||[];if(N(t))return o=(e=A(C.useState([]),2))[0],i=e[1],C.useEffect(function(){var e=t.filter(function(e){return D(null==u?void 0:u.values,e)});i(e)},[t,null==u||null==(e=u.values)?void 0:e.source]),C.createElement("div",{className:p.default("wprf-tab-content-wrapper",null==u||null==(e=u.values)?void 0:e.source,null==u||null==(e=u.values)?void 0:e.themes)},C.createElement("div",{className:"wprf-tab-flex"},C.createElement("div",{className:"wprf-tab-contents"},t.map(function(e,t){var n;return D(null==u?void 0:u.values,e)?(n=p.default("wprf-tab-content","wprf-tab-".concat(null==e?void 0:e.id),{"wprf-active":r===e.id}),C.createElement("div",{id:null==e?void 0:e.id,className:n,key:null==e?void 0:e.id},((null==e?void 0:e.label)&&(null==(n=null==a?void 0:a.title)||n)||(null==a?void 0:a.content_heading))&&C.createElement("div",{className:"wprf-tab-heading-wrapper"},(null==e?void 0:e.label)&&(null==(n=null==a?void 0:a.title)||n)&&C.createElement("h4",null,e.label),C.createElement("div",null,(null==a?void 0:a.content_heading)&&Object.keys(a.content_heading).map(function(e,t){return C.createElement(s.default.Fragment,{key:"button_".concat(e,"_").concat(t)},C.createElement(g,a.content_heading[e]))}))),C.createElement(En,{context:u,fields:null==e?void 0:e.fields,parentIndex:[].concat(w(c),[t])}))):""})),H.applyFilters("wprf_tab_content","",a)),(null==a||null==(e=a.step)?void 0:e.show)&&C.createElement(On,{fields:o,active:r,setActive:n,config:null!=(e=a.step)?e:{show:!1}}),(null==(o=null==l?void 0:l.show)||o)&&(null==l||!l.rules||I(null==l?void 0:l.rules,{rest:a}))&&C.createElement(hn,l));throw new Error(d.__("Not an array.","notificationx"))}var hn=function(e){var e=b({},((e=>{if(null==e)throw new TypeError("Cannot destructure "+e)})(e),e)),n=O(),e=(null==e?void 0:e.label)||d.__("Save Changes","notificationx"),t=C.useCallback(function(e){var t;null!=(t=n.submit)&&t.onSubmit&&n.submit.onSubmit(e,n)},[n]);return C.createElement("div",{className:"wprf-submit wprf-control"},C.createElement(m.Button,{className:"wprf-submit-button",onClick:t},e))},On=s.default.memo(function(n){var e=A(C.useState(void 0),2),r=e[0],l=e[1],e=A(C.useState(void 0),2),a=e[0],o=e[1];return O(),C.useEffect(function(){var e=n.fields.map(function(e){return e.id}),t=e.findIndex(function(e){return e===n.active});-1!=t&&o(e[t-1]),t<=e.length&&l(e[t+1])},[n.active,n.fields]),C.createElement("div",{className:"wprf-stepped-button"},n.config.buttons&&Object.keys(n.config.buttons).map(function(e,t){return C.createElement(s.default.Fragment,{key:"button_".concat(e,"_").concat(t)},("next"===e&&void 0!==r||"prev"===e&&void 0!==a)&&C.createElement(m.Button,{className:"wprf-btn wprf-step-btn-".concat(e),onClick:function(){return n.setActive("next"===e?r:a)}},null==(t=n.config.buttons)?void 0:t[e]),null==r&&(null==(t=n.config.buttons)||null==(t=t[e])?void 0:t.type)&&C.createElement(g,null==(t=n.config.buttons)?void 0:t[e]))}))}),jn=["fields","active","setActive","submit"],_n=function(e){var t=O(),n=A(C.useState(e.value||e.active),2),r=n[0],l=n[1],n=p.default("wp-react-form wprf-tabs-wrapper",null==e?void 0:e.className,{"wprf-tab-menu-as-sidebar":null==e?void 0:e.sidebar});return C.useEffect(function(){e.value!==r&&l(e.value)},[e.value]),C.useEffect(function(){e.value!==r&&e.onChange({target:{type:"button",name:e.name,value:r}})},[r]),C.createElement("div",{className:n},C.createElement(Le,b({},e,{active:r,setActive:function(e){return l(e)},fields:e.fields,context:t})),C.createElement(wn,b({},e,{fields:e.fields,active:r,setActive:function(e){return l(e)},submit:null==e?void 0:e.submit})))};function xn(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function Sn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?xn(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):xn(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}x.registerStore("formbuilder",L);e.Action=cn,e.BuilderConsumer=U,e.BuilderProvider=B,e.Button=dn,e.CodeViewer=xt,e.ColorPicker=un,e.Column=Ke,e.Date=vt,e.Editor=fn,e.Field=g,e.FormBuilder=function(e){var t,n=O(),r=e;return null!=(t=r)&&t.type||(r=Sn(Sn(Sn({},e),e.config),{},{value:e.config.active,fields:e.tabs,tabs:void 0,submit:null==e?void 0:e.submit,onChange:function(e){n.setActiveTab(null==e||null==(e=e.target)?void 0:e.value)}})),C.createElement(C.Fragment,null,C.createElement(_n,r))},e.GenericField=y,e.GenericInput=gt,e.Group=Pt,e.Image=rt,e.Input=Et,e.JsonUploader=St,e.Label=Qe,e.Media=sn,e.Message=yn,e.Modal=gn,e.ObjectFilter=function(t,n){var r,e,l=2<arguments.length&&void 0!==arguments[2]&&arguments[2];return!!t&&(r={},e=Object.keys(t).filter(function(e){return n(e)}),l?e:(e.map(function(e){r[e]=t[e]}),r))},e.Radio=Yt,e.Repeater=ln,e.ResponsiveNumber=bn,e.Row=Ye,e.Section=Kt,e.Select=Nt,e.SelectAsync=on,e.Slider=an,e.SweetAlert=function(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return oe.default.fire(function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ge(Object(n),!0).forEach(function(e){E(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ge(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({target:null!=(e=null==t?void 0:t.target)?e:"#notificationx",type:null!=(e=null==t?void 0:t.type)?e:"success",html:null==t?void 0:t.html,title:null!=(e=null==t?void 0:t.title)?e:d.__("Title Goes Here: title","notificationx"),text:null!=(e=null==t?void 0:t.text)?e:d.__("Test Goes Here: text","notificationx"),icon:null!=(e=null==t?void 0:t.icon)?e:(null==t?void 0:t.type)||"success",timer:null!=(e=null==t?void 0:t.timer)?e:null},t))},e.Textarea=Ot,e.Toggle=zt,e._extends=r,e.builderReducer=Je,e.downloadFile=Ne,e.executeChange=he,e.getIn=T,e.getSelectedValues=function(e){return Array.from(e).filter(function(e){return e.selected}).map(function(e){return e.value})},e.getStoreData=ze,e.getTime=je,e.hitAAJX=Oe,e.isArray=N,e.isEmptyObj=v,e.isExists=function(e,t){var n=o(e);switch(!0){case"object"===n&&N(e):return e.includes(t);case"object"===n&&!N(e):return void 0!==(null==e?void 0:e[t]);default:return e===t}},e.isFunction=we,e.isNumber=Ee,e.isObject=h,e.isString=k,e.isVisible=D,e.merge=_e,e.objectWithoutPropertiesLoose=ke,e.processAjaxData=function(n){var r={};return Object.keys(n).map(function(e){var t;0===n[e].indexOf("@")?""!=(t=n[e].substr(1))&&(t=ze().getFieldValue(t),r[e]=t||"undefined"):r[e]=n[e]}),r},e.setIn=n,e.setStoreData=We,e.sortingFields=F,e.triggerDefaults=function(e,t){var n,r,l=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!v(e)&&"object"===o(e))for(var a in e)a===l&&(n=e[a].indexOf("@"),r=e[a].indexOf(":"),0===n)&&0<r&&(n=e[a].substr(1,r-1),a=e[a].substr(r+1),r=ze().getSavedFieldValue(n,t),""!=n)&&""!=a&&We().setFieldValue({name:n,value:E({},n,r||a)})},e.useBuilder=function(e){var t=C.useRef(!1);C.useEffect(function(){return t.current=!0,function(){t.current=!1}},[]);var n=A(C.useReducer(Je,V(V({},e),{},{savedValues:e.savedValues||{},values:e.values||{},errors:e.initialErrors||{},touched:e.initialTouched||{},icons:e.initialIcons||{},common:{},alerts:{},tabs:F(e.tabs)})),2),u=n[0],r=n[1],n=R(function(e,t,n){r({type:"SET_CONTEXT",payload:{field:e,value:t}})}),l=R(function(e,t){e="function"==typeof e?e(u.values):e;return r({type:"SET_VALUES",payload:e}),void 0!==t&&t?e:Promise.resolve()}),a=R(function(e,t){e="function"==typeof e?e(u.values):e;return r({type:"SET_SAVED_VALUES",payload:e}),void 0!==t&&t?e:Promise.resolve()}),o=R(function(e,t,n){r({type:"SET_FIELD_VALUE",payload:{field:e,value:t}})}),i=R(function(e,t){r({type:"SET_FORM_FIELD",payload:{field:e,value:t}})}),c=C.useCallback(function(e){return T(u.values,e)},[u]),s=R(function(e,t,n){r({type:"SET_FIELD_TOUCHED",payload:{field:e,value:t=t||!0}})}),f=C.useCallback(function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],e=(e.persist&&e.persist(),e.target),n=e.name;s(t||n||e.id,!0)},[s]),d=R(function(t){if("string"==typeof t)return function(e){return f(e,t)};f(t)}),p=C.useCallback(function(e,t,n){null!=n&&n.isPro&&!1===Boolean(u.is_pro_active)||(e=(n=he(e,t)).field)&&o(e,n.val)},[o,u.values]),m=R(function(t,n){var e;if((null!=n&&n.isPro&&!1===Boolean(u.is_pro_active)||null!=n&&null!=(e=n.popup)&&e.forced)&&null!=(e=u.alerts)&&null!=(e=e.pro_alert(null==n?void 0:n.popup))&&e.fire(),null==n||!n.nx_has_permission)return"string"==typeof t?function(e){return p(t,e,n)}:void p(t,null,n);null!=(e=u.alerts)&&null!=(e=e.has_permission_alert(null==n?void 0:n.permission_popup))&&e.fire()}),v=C.useCallback(function(e){var t=V({},e),n=P(t),r=n.name,l=n.type,a=n.parent,o=n.parenttype,i=(null!=t&&t.is_pro&&(n.is_pro=!(null!=t&&t.is_pro&&!0===Boolean(u.is_pro_active))),a=a&&"group"===o?null!=(i=null==(i=null!=(i=T(u.values,a))?i:{})?void 0:i[r])?i:null==t?void 0:t.default:a&&"repeater"===o?null!=(i=null==(o=null!=(i=T(u.values,a))?i:[])||null==(a=o[n.index])?void 0:a[r])?i:null==t?void 0:t.default:null!=(o=T(u.values,r))?o:null==t?void 0:t.default,n.onChange=m,n.onBlur=d,n.value);return"checkbox"!==l||n.multiple?"radio"===l?(n.checked=a===i,n.value=i):n.value="date"===l&&null==a?je():a:(n.checked=!!a,n.value=!!a,k(a)&&"0"===a?(n.checked=!1,n.value=!1):(n.checked=Boolean(a),n.value=Boolean(a))),n.visible=D(u.values,e),n},[d,m,u.values]),b=C.useCallback(function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;return n=null!==n?null==(n=T(u.values,n))?void 0:n[e]:T(u.values,e)||(null==(n=t.meta)?void 0:n.default),V(V({},t.meta),{},{value:n,error:T(u.errors,e),touched:!!T(u.touched,e),visible:D(u.values,t),initialValue:"",initialTouched:"",initialError:""})},[u.errors,u.touched,u.values]),y=C.useCallback(function(e){return 0<e.length?e.filter(function(e){return null!=e&&e.rules?I(e.rules,u.values):e}):e},[u.errors,u.touched,u.values]),g=C.useCallback(function(e,t){var n;return e.length?(n=[],2<arguments.length&&void 0!==arguments[2]&&arguments[2]&&N(t)?e.filter(function(e){return t.includes(e.value)}):0<(n=e.filter(function(e){return e.value==t})).length?n[0]:""):e},[u.errors,u.touched,u.values]),E=C.useCallback(function(){return{setValue:function(e,t){return o(e,t)},getValue:function(e){return T(u.values,e)},getValueForDefault:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return(null===t||T(u.savedValues,t)===T(u.values,t))&&T(u.savedValues,e)}}},[u.errors,u.touched,u.values,u.savedValues]),w=C.useCallback(function(e){return T(u.tabs,e)},[u]),h=R(function(e){r({type:"SET_ISSUBMITTING",payload:e})}),O=R(function(e){r({type:"SET_ACTIVE_TAB",payload:e})}),j=R(function(e){r({type:"SET_REDIRECT",payload:e})}),_=R(function(e,t){r({type:"SET_ICONS",payload:{name:e,icons:t}})}),x=R(function(e,t){r({type:"SET_COMMONS",payload:{name:e,value:t}})}),S=R(function(e,t){r({type:"SET_ALERTS",payload:{name:e,value:t}})});return V(V(V({},e),u),{},{setContext:n,values:u.values,savedValues:u.savedValues,errors:u.errors,touched:u.touched,isSubmitting:!1,setActiveTab:O,setRedirect:j,setSubmitting:h,setValues:l,setSavedValues:a,setFieldValue:o,getFieldValue:c,handleBlur:d,handleChange:m,getFieldProps:v,getFieldMeta:b,getFieldHelpers:E,eligibleOptions:y,eligibleOption:g,getTabFields:w,setFormField:i,registerIcons:_,registerCommon:x,registerAlert:S})},e.useBuilderContext=O,e.useDefaults=He,e.validFieldProps=P,e.when=I,e.withLabel=u,e.withProps=ut,e.withState=function(e){return Boolean(["group","section"].includes(e))},e.wpFetch=Ce,Object.defineProperty(e,"__esModule",{value:!0})});
