import React,{useState,useEffect,createElement,useContext,createContext,useRef,useReducer,useCallback,useLayoutEffect,Fragment,useMemo}from"react";import{select,dispatch,registerStore}from"@wordpress/data";import{toPath,clone}from"lodash-es";import apiFetch from"@wordpress/api-fetch";import{__experimentalGetSettings,date}from"@wordpress/date";import moment from"moment";import intersect from"intersect";import{sprintf,__}from"@wordpress/i18n";import classNames from"classnames";import{applyFilters}from"@wordpress/hooks";import Swal from"sweetalert2";import{Dropdown,Button as Button$2,DateTimePicker,Icon,RangeControl,ColorPicker as ColorPicker$2}from"@wordpress/components";import copy from"copy-to-clipboard";import ReactSelect from"react-select";import{ReactSortable}from"react-sortablejs";import parse from"html-react-parser";import AsyncSelect from"react-select/async";import{MediaUpload}from"@wordpress/media-utils";import{Editor as Editor$2}from"react-draft-wysiwyg";import{EditorState,ContentState,convertToRaw}from"draft-js";import draftToHtml from"draftjs-to-html";import htmlToDraft from"html-to-draftjs";import"react-draft-wysiwyg/dist/react-draft-wysiwyg.css";import SweetAlert$1 from"react-bootstrap-sweetalert";function _typeof$1(e){return(_typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function toPrimitive(e,t){if("object"!=_typeof$1(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof$1(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}function toPropertyKey(e){e=toPrimitive(e,"string");return"symbol"==_typeof$1(e)?e:e+""}function _defineProperty(e,t,n){return(t=toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _arrayLikeToArray$2(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray$2(e)}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _unsupportedIterableToArray$2(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$2(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$2(e,t):void 0}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray$2(e)||_nonIterableSpread()}function ownKeys$g(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$g(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$g(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var wpFetch=function(e){e=_objectSpread$g(_objectSpread$g({},e),{},{method:"POST"});return apiFetch(e)},isString=function(e){return null!==e&&"string"==typeof e},isNumber=function(e){return null!==e&&"number"==typeof e},isInteger=function(e){return String(Math.floor(Number(e)))===e},isFunction=function(e){return null!==e&&"function"==typeof e},isArray=function(e){return null!==e&&"object"===_typeof$1(e)&&Array.isArray(e)},isObject=function(e){return null!==e&&"object"===_typeof$1(e)&&!isArray(e)},isVisible=function(e,t){return null==t||!t.rules||null==t.name||(t=when(t.rules,e),Boolean(t))},withState=function(e){return Boolean(["group","section"].includes(e))},isEmptyObj=function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},getIn=function(e,t,n,r){void 0===r&&(r=0);for(var o=toPath(t);e&&r<o.length;)e=e[o[r++]];return void 0===e?n:e},sortingFields=function(e){return[].concat(e).sort(function(e,t){return null==e.priority||null==t.priority?0:e.priority>t.priority?1:-1})},getSelectedValues=function(e){return Array.from(e).filter(function(e){return e.selected}).map(function(e){return e.value})},executeChange=function(e,t){var n=t,r=e;if(!isString(e)){e.persist&&e.persist();var e=e.target||e.currentTarget,o=e.type,l=e.value,a=e.checked,i=e.multiple,n=t||e.name;switch(o){case"number":case"range":r=parseFloat(l);break;case"checkbox":r=i?l:a;break;default:r=l}}return{field:n,val:r}},objectWithoutPropertiesLoose=function(e,t){if(null==e)return{};for(var n,r={},o=Object.keys(e),l=0;l<o.length;l++)n=o[l],0<=t.indexOf(n)||(r[n]=e[n]);return r},setIn=function(e,t,n){for(var r=clone(e),o=r,l=0,a=toPath(t);l<a.length-1;l++){var i=a[l],u=getIn(e,a.slice(0,l+1));o=u&&(isObject(u)||Array.isArray(u))?o[i]=clone(u):(u=a[l+1],o[i]=isInteger(u)&&0<=Number(u)?[]:{})}return(0===l?e:o)[a[l]]===n?e:(void 0===n?delete o[a[l]]:o[a[l]]=n,0===l&&void 0===n&&delete r[a[l]],r)},validFieldProps=function(e){var t=e.type,n=["validation_rules","default","rules","meta","switch"].concat(_toConsumableArray(1<arguments.length&&void 0!==arguments[1]?arguments[1]:[])),t=("select"!==t&&"select-async"!==t&&"radio-card"!==t&&"checkbox"!==t&&"toggle"!==t&&e.multiple&&n.push("options"),"tab"!==t&&"group"!==t&&"repeater"!==t&&"section"!==t&&"button"!==t&&n.push("fields"),objectWithoutPropertiesLoose(e,n));return null==e||!e.label||null!=e&&e.placeholder||(t.placeholder=e.label),t},hitAAJX=function(o){var l=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(null!==l&&o){var r,e=!0;if(e=null!=o&&o.rules?when(null==o?void 0:o.rules,l.values):e)return r={},Object.keys(o.data).map(function(e){var t,n;-1<(null==(n=(t=o.data[e]).indexOf)?void 0:n.call(t,"@"))?(n=o.data[e].substr(1),r[e]=null==(t=l.values)?void 0:t[n]):r[e]=o.data[e]}),wpFetch({path:o.api,data:r}).then(function(e){"success"==(null==e?void 0:e.status)&&null!=e&&e.redirect&&(window.location=null==e?void 0:e.redirect);var t,n,r=null!=e&&null!=(t=e.data)&&t.context?e.data.context:!(null==e||!e.context)&&e.context;return r&&isObject(r)&&Object.keys(r).map(function(e){l.setFieldValue(e,r[e])}),null!=e&&null!=(t=e.data)&&t.download&&downloadFile({data:JSON.stringify(e.data.download),fileName:(null==e||null==(t=e.data)?void 0:t.filename)||"export.json",fileType:"text/json"}),null!=o&&o.trigger&&isString(null==o?void 0:o.trigger)&&(t=o.trigger.indexOf("@"),n=o.trigger.indexOf(":"),0===t)&&0<n&&(t=o.trigger.substr(1,n-1),"true"==(n=o.trigger.substr(n+1))?n=!0:"false"==n&&(n=!1),l.setFieldValue(t,n)),e})}return Promise.reject(!1)},getTime=function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=__experimentalGetSettings();return moment.utc(e||void 0).utcOffset(+(null==n||null==(e=n.timezone)?void 0:e.offset),t)},merge=function(e,t,n){var r=_toConsumableArray(e),e=t.filter(function(t){return r.findIndex(function(e){return e[n]===t[n]})<=-1});return[].concat(_toConsumableArray(r),_toConsumableArray(e))},downloadFile=function(e){var t=e.data,n=e.fileName,t=new Blob([t],{type:e.fileType}),e=document.createElement("a"),n=(e.download=n,e.href=window.URL.createObjectURL(t),new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}));e.dispatchEvent(n),e.remove()},_typeof=function(e){return("function"!=typeof Symbol||"symbol"!==_typeof$1(Symbol.iterator))&&e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof$1(e)},get=function(e,t){var n=!(2<(arguments.length<=2?0:arguments.length-2)&&void 0!==(arguments.length<=4?void 0:arguments[4]))||arguments.length<=4?void 0:arguments[4];return String.prototype.split.call(t,/[,[\].]+?/).filter(Boolean).reduce(function(e,t){return e&&Object.hasOwnProperty.call(e,t)?e[t]:n},e)},rules={is:function(e,t,n){return get(n,e)==t},"!is":function(e,t,n){return!rules.is(e,t,n)},includes:function(e,t,n){if(!isEmptyObj(n)){n=get(n,e);if("function"!=_typeof(n)){if(isArray(t)&&isArray(n))return!(null==(e=intersect(n,t))||!e.length);if(isArray(t)&&"string"==_typeof(n))return t.includes(n);if(isArray(n)&&"string"==_typeof(t))return n.includes(t)}}return!1},"!includes":function(e,t,n){return!rules.includes(e,t,n)},isOfType:function(e,t,n){return _typeof(get(n,e))===t},"!isOfType":function(e,t,n){return!rules.isOfType(e,t,n)},allOf:function(e,t,n){var r;if(Array.isArray(t))return r=get(n,e),t.every(function(e){return r.includes(e)});throw Error(__('"allOf" condition requires an array as #3 argument',"notificationx"))},anyOf:function(e,t,n){if(Array.isArray(t))return n=get(n,e),t.includes(n);throw Error(__('"anyOf" condition requires an array as #3 argument',"notificationx"))},gt:function(e,t,n){return get(n,e)>t},gte:function(e,t,n){return get(n,e)>=t},lt:function(e,t,n){return get(n,e)<t},lte:function(e,t,n){return get(n,e)<=t}},logicalRules={and:function(e){return!e.includes(!1)},or:function(e){return e.includes(!0)},not:function(e){if(1!==e.length)throw Error(__('"not" can have only one comparison rule, multiple rules given',"notificationx"));return!e[0]}},isValidCondition=function(e){return!!(Array.isArray(e)&&Array.isArray(e[1])&&e[0]&&logicalRules[e[0].toLowerCase()])},processRule=function(e,t){var n=e[0],r=e[1],e=e[2];if("string"!=typeof n||void 0===rules[n])throw Error(sprintf(__("Invalid comparison rule %s.","notificationx"),n));return rules[n](r,e,t)},processCondition=function(e,t){return logicalRules[e.toLowerCase()](t)},validate$1=function(e,n){var t,r;return isValidCondition(e)?(t=e.slice(0,1)[0],r=e.slice(1).map(function(e,t){return(isValidCondition(e)?when:processRule)(e,n)}),processCondition(t,r)):processRule(e,n)},when=function(e,t){return"function"==typeof e?Promise.resolve(e(t)):validate$1(e,t)};function ownKeys$f(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$f(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$f(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var DEFAULT_STATE={savedValues:{type:"conversions",source:"edd"},values:{},touched:{},errors:{}},actions={setSavedValues:function(e){return{type:"SET_SAVED_VALUES",payload:e}},setFieldValue:function(e){return{type:"FIELD_VALUE",name:e.name,payload:e.value}},removeFieldValue:function(e){return{type:"REMOVE_FIELD_VALUE",payload:e}},resetFieldValue:function(e){return{type:"RESET_FIELD_VALUE",payload:e}},setFieldTouched:function(e){return{type:"FIELD_TOUCHED",payload:e}},setError:function(e){return{type:"FIELD_ERROR",payload:e}},removeError:function(e){return{type:"REMOVE_FIELD_ERROR",payload:e}}},store={reducer:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:DEFAULT_STATE,t=1<arguments.length?arguments[1]:void 0;switch(t.type){case"SET_SAVED_VALUES":var n=_objectSpread$f({},e);return _objectSpread$f(_objectSpread$f({},n),{},{values:t.payload,savedValues:t.payload});case"FIELD_VALUE":var n=_objectSpread$f({},e),r=t.payload;return _objectSpread$f(_objectSpread$f({},n),{},{values:_objectSpread$f(_objectSpread$f({},null==(n=n)?void 0:n.values),r)});case"REMOVE_FIELD_VALUE":n=_objectSpread$f({},e),r=t.payload;return null!=(o=n.values)&&o[r]&&delete n.values[r],n;case"RESET_FIELD_VALUE":var o=_objectSpread$f({},e);return null!=(r=o.values)&&r[t.payload]&&(delete o.values[t.payload],null!=(n=o.savedValues))&&n[t.payload]&&(o.values[t.payload]=o.savedValues[t.payload]),o;case"FIELD_ERROR":return _objectSpread$f(_objectSpread$f({},e),{},{errors:_objectSpread$f(_objectSpread$f({},e.errors),t.payload)});case"REMOVE_FIELD_ERROR":r=_objectSpread$f({},e);return delete r.errors[t.payload],r;case"FIELD_TOUCHED":return _objectSpread$f(_objectSpread$f({},e),{},{touched:_objectSpread$f(_objectSpread$f({},e.touched),t.payload)})}return e},actions:actions,selectors:{getValues:function(e){return e.values},getFieldValue:function(e,t){return null==(e=e.values)?void 0:e[t]},getSavedFieldValue:function(e,t,n){var r;return(null===n||(null==(r=e.savedValues)?void 0:r[n])===(null==(r=e.values)?void 0:r[n]))&&(null==(r=e.savedValues)?void 0:r[t])},isTouched:function(e,t){return null==(e=e.touched)?void 0:e[t]},getError:function(e,t){return null==(e=e.errors)?void 0:e[t]},isVisible:function(e,t){return!t.rules||null==t.name||(t=when(t.rules,e.values),Boolean(t))}}};function _extends$1(){return(_extends$1=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)!{}.hasOwnProperty.call(r,n)||(e[n]=r[n])}return e}).apply(null,arguments)}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,l,a,i=[],u=!0,c=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=l.call(n)).done)&&(i.push(r.value),i.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return i}}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray$2(e,t)||_nonIterableRest()}var addCookiesListItemCount=function(t,e){var n=["necessary_cookie_lists","functional_cookie_lists","analytics_cookie_lists","performance_cookie_lists","advertising_cookie_lists","uncategorized_cookie_lists"].map(function(e){return Array.isArray(null==t?void 0:t.values[e])?null==t||null==(e=t.values[e])?void 0:e.length:0});e.forEach(function(e,t){e.count=n[t]})},Menu=function(r){if(void 0===r.fields)throw new Error(__("There are no tabs defined!","notificationx"));var o=r.active,l=r.setActive,t=r.fields,a=r.context,e=_slicedToArray(useState([]),2),n=e[0],i=e[1],e=(null!=r&&r.dataShare&&addCookiesListItemCount(a,t),useEffect(function(){var e=t.filter(function(e){return isVisible(null==a?void 0:a.values,e)});i(e)},[t,null==a||null==(e=a.values)?void 0:e.source]),classNames("wprf-tab-menu-wrapper",null==r?void 0:r.className,{"wprf-tab-menu-sidebar":null==r?void 0:r.sidebar},null==a||null==(e=a.values)?void 0:e.source)),u=n.findIndex(function(e){return e.id===o});return createElement("div",{className:e},createElement("ul",{className:"wprf-tab-nav"},n.map(function(t,e){var n;return createElement("li",{className:classNames("wprf-tab-nav-item",_defineProperty(_defineProperty(_defineProperty({},"".concat(t.classes),t.classes),"wprf-active-nav",o===t.id),"wprf-tab-complete",!(null==r||!r.completionTrack)&&e<=u)),"data-key":t.id,key:t.id,onClick:function(){var e;return(null==(e=null==r?void 0:r.clickable)||e)&&l(t.id)}},(null==t?void 0:t.icon)&&(isString(t.icon)&&!isObject(t.icon)?createElement("img",{src:t.icon,alt:null==t?void 0:t.label}):isObject(t.icon)?null==a||null==(e=a.icons)||null==(e=e[null==t||null==(n=t.icon)?void 0:n.type])?void 0:e[null==t||null==(n=t.icon)?void 0:n.name]:""),createElement("span",null,t.label),(null==r?void 0:r.dataShare)&&createElement("span",{className:"list-count"},(null==t?void 0:t.count)<10?"0".concat(null==t?void 0:t.count):null==t?void 0:t.count))})))};function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n,r={};for(n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}function _objectWithoutProperties(e,t){if(null==e)return{};var n,r=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols)for(var o=Object.getOwnPropertySymbols(e),l=0;l<o.length;l++)n=o[l],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n]);return r}var BuilderContext=createContext(void 0),BuilderProvider=(BuilderContext.displayName="production"===process.env.NODE_ENV?"Anonymous":"BuilderContext",BuilderContext.Provider),BuilderConsumer=BuilderContext.Consumer;function useBuilderContext(){return useContext(BuilderContext)}var useOptions=function(t){var n,r,o,l,e,a,i,u,c,s,d,p,f,m,v=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"fields";if(null!=t&&t[v])return n=t.value,r=t.multiple,o=useBuilderContext(),c=_slicedToArray(useState(t[v]),2),l=c[0],e=c[1],c=_slicedToArray(useState([]),2),a=c[0],i=c[1],c=_slicedToArray(useState({options:null,parentIndex:null}),2),u=c[0],c=c[1],p=_slicedToArray(useState(null),2),s=p[0],d=p[1],p=_slicedToArray(useState(null),2),f=p[0],m=p[1],useEffect(function(){var e=t.ajax&&(null==(e=o.getTabFields(null==t?void 0:t.parentIndex))?void 0:e[v])||l;i(o.eligibleOptions(e)),d(o.eligibleOption(e,n,null!=r&&r))},[n,l]),useEffect(function(){e(t[v]),i(o.eligibleOptions(t[v]))},[t]),useEffect(function(){i(o.eligibleOptions(l))},[l]),useEffect(function(){null!=u.options&&e(u.options)},[u]),useEffect(function(){var e;null!=s&&(e=r?isArray(s)&&s.map(function(e){return e.value})||n:s.value||n,m(e))},[s]),useEffect(function(){var e;0===a.filter(function(e){return e.value===f}).length&&(e=sortingFields(a),m((null==e||null==(e=e[0])?void 0:e.value)||n))},[f,a]),{options:sortingFields(a),option:f,selectedOption:s,setOptions:i,setData:c};throw new Error("#options param need to set in order to use useOptions hook.")},useTrigger=function(e){var o=e.context;null!=e&&e.trigger&&isArray(null==e?void 0:e.trigger)&&null!=e&&e.trigger.map(function(e){var t=(null==e?void 0:e.type)||"setFieldValue";if(null!=e&&e.action&&isObject(null==e?void 0:e.action))for(var n in null==e?void 0:e.action){var r=n,n=(-1<r.indexOf(".")&&(r=r.split(".")),null==e?void 0:e.action[n]);""!=r&&o[t](r,n)}})},useDefaults=function(e,t,n,r){if(null!=r&&null!=(null==r?void 0:r.defaults)&&!isEmptyObj(r.defaults)){var o=r.defaults;if(null!=o&&!isEmptyObj(o)){var l={};if(null!=o&&o[n]&&isString(null==o?void 0:o[n])){var a,r=o[n].indexOf("@"),i=o[n].indexOf(":");0===r&&0<i&&(r=o[n].substr(1,i-1),i=o[n].substr(i+1),a=t.getValueForDefault(r,e),""!=r)&&""!=i&&(i="false"!==i&&i,l[r]=a||i,t.setValue(r,a||i))}else if(null!=o&&o[n]&&(isArray(o[n])||isObject(o[n])))for(var u in o[n]){var c,s,d=o[n][u];d&&(isArray(d)||isObject(d))?(c=t.getValueForDefault(u,e),""!=u&&""!=d&&(d="false"!==d&&d,l[u]=c||d,t.setValue(u,c||d))):d&&(u=d.indexOf("@"),c=d.indexOf(":"),0===u)&&0<c&&(u=d.substr(1,c-1),s=d.substr(c+1),-1<d.indexOf(".")&&(u=u.split(".")),d=t.getValueForDefault(u,e),""!=u)&&""!=s&&(s="false"!==s&&s,l[u]=d||s,t.setValue(u,d||s))}return{defaultsData:l}}}};function ownKeys$e(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$e(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$e(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$e(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var SweetAlert=function(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return Swal.fire(_objectSpread$e({target:null!=(e=null==t?void 0:t.target)?e:"#notificationx",type:null!=(e=null==t?void 0:t.type)?e:"success",html:null==t?void 0:t.html,title:null!=(e=null==t?void 0:t.title)?e:__("Title Goes Here: title","notificationx"),text:null!=(e=null==t?void 0:t.text)?e:__("Test Goes Here: text","notificationx"),icon:null!=(e=null==t?void 0:t.icon)?e:(null==t?void 0:t.type)||"success",timer:null!=(e=null==t?void 0:t.timer)?e:null},t))},ObjectFilter=function(t,n){var r,e,o=2<arguments.length&&void 0!==arguments[2]&&arguments[2];return!!t&&(r={},e=Object.keys(t).filter(function(e){return n(e)}),o?e:(e.map(function(e){r[e]=t[e]}),r))},isExists=function(e,t){var n=_typeof$1(e);switch(!0){case"object"===n&&isArray(e):return e.includes(t);case"object"===n&&!isArray(e):return void 0!==(null==e?void 0:e[t]);default:return e===t}},triggerDefaults=function(e,t){var n,r,o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!isEmptyObj(e)&&"object"===_typeof$1(e))for(var l in e)l===o&&(n=e[l].indexOf("@"),r=e[l].indexOf(":"),0===n)&&0<r&&(n=e[l].substr(1,r-1),l=e[l].substr(r+1),r=getStoreData().getSavedFieldValue(n,t),""!=n)&&""!=l&&setStoreData().setFieldValue({name:n,value:_defineProperty({},n,r||l)})},getStoreData=function(){return select("formbuilder")},setStoreData=function(){return dispatch("formbuilder")},processAjaxData=function(n){var r={};return Object.keys(n).map(function(e){var t;0===n[e].indexOf("@")?""!=(t=n[e].substr(1))&&(t=getStoreData().getFieldValue(t),r[e]=t||"undefined"):r[e]=n[e]}),r};function _extends(){for(var e=arguments.length,o=new Array(e),t=0;t<e;t++)o[t]=arguments[t];return(Object.assign||function(e){for(var t=1;t<o.length;t++){var n,r=o[t];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,o)}function ownKeys$d(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$d(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$d(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var builderReducer=function(e,t){switch(t.type){case"SET_CONTEXT":return _extends({},e,setIn(e,t.payload.field,t.payload.value));case"SET_ACTIVE_TAB":return _objectSpread$d(_objectSpread$d({},e),{},{config:_objectSpread$d(_objectSpread$d({},e.config),{},{active:t.payload})});case"SET_REDIRECT":return _objectSpread$d(_objectSpread$d({},e),{},{redirect:_objectSpread$d(_objectSpread$d({},e.redirect),t.payload)});case"SET_VALUES":return _extends({},e,setIn(e,"values",t.payload));case"SET_SAVED_VALUES":return _extends({},e,setIn(e,"savedValues",t.payload));case"SET_FIELD_VALUE":return _extends({},e,{values:setIn(e.values,t.payload.field,t.payload.value)});case"SET_TOUCHED":return _extends({},e,{touched:t.payload});case"SET_ERRORS":return _extends({},e,{errors:t.payload});case"SET_STATUS":return _extends({},e,{status:t.payload});case"SET_ISSUBMITTING":return _objectSpread$d(_objectSpread$d({},e),{},{isSubmitting:t.payload});case"SET_ISVALIDATING":return _extends({},e,{isValidating:t.payload});case"SET_FIELD_TOUCHED":return _objectSpread$d(_objectSpread$d({},e),{},{touched:_objectSpread$d(_objectSpread$d({},e.touched),{},_defineProperty({},t.payload.field,t.payload.value))});case"SET_FIELD_ERROR":case"RESET_FORM":return _extends({},e,t.payload);case"SUBMIT_ATTEMPT":return _extends({},e,{isSubmitting:!0,submitCount:e.submitCount+1});case"SUBMIT_FAILURE":case"SUBMIT_SUCCESS":return _extends({},e,{isSubmitting:!1});case"SET_FORM_FIELD":return null===t.payload.field?_extends({},e,setIn(e,"tabs",t.payload.value)):_extends({},e,{tabs:setIn(e.tabs,t.payload.field,t.payload.value)});case"SET_ICONS":return _extends({},e,{icons:setIn(e.icons,t.payload.name,t.payload.icons)});case"SET_ALERTS":return _extends({},e,{alerts:setIn(e.alerts,t.payload.name,t.payload.value)});case"SET_COMMONS":return _extends({},e,{common:setIn(e.common,t.payload.name,t.payload.value)});default:return e}};function ownKeys$c(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$c(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$c(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var useBuilder=function(e){var t=useRef(!1);useEffect(function(){return t.current=!0,function(){t.current=!1}},[]);var n=_slicedToArray(useReducer(builderReducer,_objectSpread$c(_objectSpread$c({},e),{},{savedValues:e.savedValues||{},values:e.values||{},errors:e.initialErrors||{},touched:e.initialTouched||{},icons:e.initialIcons||{},common:{},alerts:{},tabs:sortingFields(e.tabs)})),2),u=n[0],r=n[1],n=useEventCallback(function(e,t,n){r({type:"SET_CONTEXT",payload:{field:e,value:t}})}),o=useEventCallback(function(e,t){e="function"==typeof e?e(u.values):e;return r({type:"SET_VALUES",payload:e}),void 0!==t&&t?e:Promise.resolve()}),l=useEventCallback(function(e,t){e="function"==typeof e?e(u.values):e;return r({type:"SET_SAVED_VALUES",payload:e}),void 0!==t&&t?e:Promise.resolve()}),a=useEventCallback(function(e,t,n){r({type:"SET_FIELD_VALUE",payload:{field:e,value:t}})}),i=useEventCallback(function(e,t){r({type:"SET_FORM_FIELD",payload:{field:e,value:t}})}),c=useCallback(function(e){return getIn(u.values,e)},[u]),s=useEventCallback(function(e,t,n){r({type:"SET_FIELD_TOUCHED",payload:{field:e,value:t=t||!0}})}),d=useCallback(function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],e=(e.persist&&e.persist(),e.target),n=e.name;s(t||n||e.id,!0)},[s]),p=useEventCallback(function(t){if("string"==typeof t)return function(e){return d(e,t)};d(t)}),f=useCallback(function(e,t,n){null!=n&&n.isPro&&!1===Boolean(u.is_pro_active)||(e=(n=executeChange(e,t)).field)&&a(e,n.val)},[a,u.values]),m=useEventCallback(function(t,n){var e;if((null!=n&&n.isPro&&!1===Boolean(u.is_pro_active)||null!=n&&null!=(e=n.popup)&&e.forced)&&null!=(e=u.alerts)&&null!=(e=e.pro_alert(null==n?void 0:n.popup))&&e.fire(),null==n||!n.nx_has_permission)return"string"==typeof t?function(e){return f(t,e,n)}:void f(t,null,n);null!=(e=u.alerts)&&null!=(e=e.has_permission_alert(null==n?void 0:n.permission_popup))&&e.fire()}),v=useCallback(function(e){var t=_objectSpread$c({},e),n=validFieldProps(t),r=n.name,o=n.type,l=n.parent,a=n.parenttype,i=(null!=t&&t.is_pro&&(n.is_pro=!(null!=t&&t.is_pro&&!0===Boolean(u.is_pro_active))),l=l&&"group"===a?null!=(i=null==(i=null!=(i=getIn(u.values,l))?i:{})?void 0:i[r])?i:null==t?void 0:t.default:l&&"repeater"===a?null!=(i=null==(a=null!=(i=getIn(u.values,l))?i:[])||null==(l=a[n.index])?void 0:l[r])?i:null==t?void 0:t.default:null!=(a=getIn(u.values,r))?a:null==t?void 0:t.default,n.onChange=m,n.onBlur=p,n.value);return"checkbox"!==o||n.multiple?"radio"===o?(n.checked=l===i,n.value=i):n.value="date"===o&&null==l?getTime():l:(n.checked=!!l,n.value=!!l,isString(l)&&"0"===l?(n.checked=!1,n.value=!1):(n.checked=Boolean(l),n.value=Boolean(l))),n.visible=isVisible(u.values,e),n},[p,m,u.values]),b=useCallback(function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;return n=null!==n?null==(n=getIn(u.values,n))?void 0:n[e]:getIn(u.values,e)||(null==(n=t.meta)?void 0:n.default),_objectSpread$c(_objectSpread$c({},t.meta),{},{value:n,error:getIn(u.errors,e),touched:!!getIn(u.touched,e),visible:isVisible(u.values,t),initialValue:"",initialTouched:"",initialError:""})},[u.errors,u.touched,u.values]),y=useCallback(function(e){return 0<e.length?e.filter(function(e){return null!=e&&e.rules?when(e.rules,u.values):e}):e},[u.errors,u.touched,u.values]),g=useCallback(function(e,t){var n;return e.length?(n=[],2<arguments.length&&void 0!==arguments[2]&&arguments[2]&&isArray(t)?e.filter(function(e){return t.includes(e.value)}):0<(n=e.filter(function(e){return e.value==t})).length?n[0]:""):e},[u.errors,u.touched,u.values]),_=useCallback(function(){return{setValue:function(e,t){return a(e,t)},getValue:function(e){return getIn(u.values,e)},getValueForDefault:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return(null===t||getIn(u.savedValues,t)===getIn(u.values,t))&&getIn(u.savedValues,e)}}},[u.errors,u.touched,u.values,u.savedValues]),w=useCallback(function(e){return getIn(u.tabs,e)},[u]),h=useEventCallback(function(e){r({type:"SET_ISSUBMITTING",payload:e})}),E=useEventCallback(function(e){r({type:"SET_ACTIVE_TAB",payload:e})}),O=useEventCallback(function(e){r({type:"SET_REDIRECT",payload:e})}),j=useEventCallback(function(e,t){r({type:"SET_ICONS",payload:{name:e,icons:t}})}),S=useEventCallback(function(e,t){r({type:"SET_COMMONS",payload:{name:e,value:t}})}),x=useEventCallback(function(e,t){r({type:"SET_ALERTS",payload:{name:e,value:t}})});return _objectSpread$c(_objectSpread$c(_objectSpread$c({},e),u),{},{setContext:n,values:u.values,savedValues:u.savedValues,errors:u.errors,touched:u.touched,isSubmitting:!1,setActiveTab:E,setRedirect:O,setSubmitting:h,setValues:o,setSavedValues:l,setFieldValue:a,getFieldValue:c,handleBlur:p,handleChange:m,getFieldProps:v,getFieldMeta:b,getFieldHelpers:_,eligibleOptions:y,eligibleOption:g,getTabFields:w,setFormField:i,registerIcons:j,registerCommon:S,registerAlert:x})},useIsomorphicLayoutEffect="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?useLayoutEffect:useEffect,useEventCallback=function(e){var l=useRef(e);return useIsomorphicLayoutEffect(function(){l.current=e}),useCallback(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var r=arguments.length,t=new Array(r),o=0;o<r;o++)t[o]=arguments[o];return l.current.apply(void 0,t)},[])},Row=function(e){var t=classNames("wprf-row clearfix wprf-flex",null==e?void 0:e.className);return createElement("div",{className:t},null==e?void 0:e.children)},Column=function(e){var t=classNames("wprf-column",null==e?void 0:e.className,_defineProperty(_defineProperty({},"wprf-column-".concat(12/(null==e?void 0:e.column)),(null==e?void 0:e.column)&&12!==e.column),"wprf-column-12",12===e.column));return createElement("div",{className:t},null==e?void 0:e.children)},Label=function(e){var t=classNames("wprf-input-label",null==e?void 0:e.className);return createElement("label",{htmlFor:null==e?void 0:e.htmlFor,className:t},(null==e||null==(t=e.badge)?void 0:t.value)&&createElement("div",{className:"wprf-badge"},createElement("sup",{className:classNames("wprf-badge-item",{"wprf-badge-active":null==e||null==(t=e.badge)?void 0:t.active})},null==e||null==(t=e.badge)?void 0:t.label)),!(null!=e&&e.src)&&(null==e?void 0:e.children),(null==e?void 0:e.src)&&createElement(Image,{className:"wprf-label-image",src:e.src,alt:null==e?void 0:e.label}))},Image=function(e){var t;return null!=e&&e.src?(t=classNames(["wprf-input-image",null==e?void 0:e.className]),createElement("img",{className:t,src:null==e?void 0:e.src,alt:null==e?void 0:e.alt})):createElement("p",null,"No Source( src ) Defined")},BadgeComp=function(e){var t=e.componentClasses;return createElement("div",{className:"wprf-badge"},createElement("sup",{className:t},e.label))},Badge=function(t){var n=useBuilderContext(),e=t.label,r=t.position,r=void 0===r?"right":r,o=t.renderLabel,l=t.renderComponent,a=(void 0===e&&(e="Pro"),classNames("wprf-badge-item",{"wprf-badge-active":t.active})),i={};return n.is_pro_active||(i={onClick:function(e){e.preventDefault(),n.alerts.pro_alert(null==t?void 0:t.popup).fire()}}),createElement("div",_extends$1({className:classNames("wprf-badge-wrapper",{"pro-deactivated":!n.is_pro_active})},i),"left"===r&&0<e.length&&createElement(Fragment,null,o(createElement(BadgeComp,{componentClasses:a,label:e}),"left")),"right"===r&&0<e.length&&createElement(Fragment,null,o(createElement(BadgeComp,{componentClasses:a,label:e}),"right")),l())},_excluded$4=["id","label","badge","badgePosition","info","context","tooltip"],ControlLabel=function(e){var t=e.id,n=e.label,r=e.badge,o=e.badgePosition,l=e.info,a=e.context,i=e.tooltip,e=_objectWithoutProperties(e,_excluded$4);return n&&0<n.length?createElement("div",{className:"wprf-control-label"},"left"==o&&r,createElement("label",{htmlFor:t},n),i&&createElement("div",{className:"wprf-control-label-tooltip"},createElement("img",{src:null==i?void 0:i.icon}),createElement("div",{dangerouslySetInnerHTML:{__html:null==i?void 0:i.content}})),l&&createElement("div",{className:"wprf-info"},createElement("button",{className:"wprf-info-button"},"Info"),createElement("p",{className:"wprf-info-text"},createElement("span",{dangerouslySetInnerHTML:{__html:l}}))),(null==e?void 0:e.link)&&createElement("a",{rel:"nofollow",target:"_blank",href:e.link},null==a||null==(t=a.icons)?void 0:t.link),"right"==o&&r):null},ControlField=function(e){var t=e.position,n=e.description,r=e.renderComponent,e=e.help;return createElement("div",{className:"wprf-control-field"},"left"===t&&n&&createElement("p",{className:"wprf-description",dangerouslySetInnerHTML:{__html:n}}),r(),"right"===t&&n&&createElement("p",{className:"wprf-description",dangerouslySetInnerHTML:{__html:n}}),e&&createElement("p",{className:"wprf-help",dangerouslySetInnerHTML:{__html:e}}))},_excluded$3=["label","id","name","type","style","is_pro","badge"];function ownKeys$b(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$b(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$b(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$b(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var withLabel=function(d){return function(t){var n,r=t.label,o=t.id,e=t.name,l=t.type,a=t.style,i=t.is_pro,u=t.badge,c=_objectWithoutProperties(t,_excluded$3),s=(null==o&&(o=e),_objectSpread$b({description:{position:"right"}},a)),a=classNames(_defineProperty(_defineProperty(_defineProperty({},"wprf-style-".concat(null==s?void 0:s.type),(null==s?void 0:s.type)||!1),"wprf-label-none",void 0===r||""===r||0===r.length),"wprf-".concat((null==s||null==(a=s.label)?void 0:a.position)||"inline","-label"),(null==(a=null==s||null==(a=s.label)?void 0:a.position)||a)&&null!=r));return"hidden"===l?createElement(d,_extends$1({},t,{id:o})):(n=validFieldProps(t,["description","label","help","style"]),l=classNames("wprf-control-wrapper","wprf-type-".concat(l),a,null==t?void 0:t.classes,_defineProperty({},"wprf-name-".concat(e),e)),createElement("div",{className:l},1==i&&createElement(Fragment,null,createElement(Badge,_extends$1({},u,c,{renderLabel:function(e,t){return createElement(ControlLabel,_extends$1({},n,{context:null==c?void 0:c.context,id:o,label:r,badge:e,badgePosition:t}))},renderComponent:function(){var e;return createElement(ControlField,{help:null,description:null==t?void 0:t.description,position:null==s||null==(e=s.description)?void 0:e.position,renderComponent:function(){return createElement(d,_extends$1({},n,{disable:!0,id:o}))}})}})),(null==t?void 0:t.help)&&createElement("div",{className:"wprf-badge-wrapper"},createElement("div",{className:"wprf-control-label"}),createElement("div",{className:"wprf-control-field"},createElement("p",{className:"wprf-help",dangerouslySetInnerHTML:{__html:t.help}})))),(0==i||null==i)&&createElement(Fragment,null,r&&0<r.length&&createElement(ControlLabel,_extends$1({},n,{context:null==c?void 0:c.context,label:r,id:o})),createElement(ControlField,{help:null==t?void 0:t.help,description:null==t?void 0:t.description,position:null==s||null==(a=s.description)?void 0:a.position,renderComponent:function(){return createElement(d,_extends$1({},n,{id:o}))}}))))}},withProps=function(u){var c=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return function(n){var e=useBuilderContext(),t=n.trigger,r=e.getFieldProps(n),o=e.getFieldMeta(r.name,n),l=e.getFieldHelpers(),a=(null!=e&&e.quickBuilder&&null!=e&&e.show&&(e.show.includes(n.name)||(r.classes=null!=r&&r.classes?r.classes+" hidden":" hidden")),null!=n&&n.parentIndex?_toConsumableArray(n.parentIndex):[]),i=(r.parentIndex=a,r.context=e,isFunction(n.onChange)&&(r.onChange=n.onChange),isFunction(n.onBlur)&&(r.onBlur=n.onBlur),useRef({}));return useEffect(function(){return i.current[n.name]=!0,function(){i.current[n.name]=!1}},[]),useEffect(function(){var e,t;["ft_theme_three_line_one","ft_theme_three_line_two","ft_theme_four_line_two"].includes(n.name)||o.visible&&i.current[n.name]&&(c||"group"===r.type?(e=null==n?void 0:n.parent,t=null==n?void 0:n.parenttype,e&&"group"===t&&r.value&&l.setValue([e,r.name],r.value)):l.setValue(r.name,r.value))},[o.visible]),useEffect(function(){i.current[n.name]&&isObject(t)&&!isEmptyObj(t)&&useDefaults(r.name,l,r.value,t)},[r.value,o.visible]),o.visible?createElement(u,r):createElement(Fragment,null)}};function _createForOfIteratorHelper$1(e,t){var n,r,o,l,a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return o=!(r=!0),{s:function(){a=a.call(e)},n:function(){var e=a.next();return r=e.done,e},e:function(e){o=!0,n=e},f:function(){try{r||null==a.return||a.return()}finally{if(o)throw n}}};if(Array.isArray(e)||(a=_unsupportedIterableToArray$1(e))||t&&e&&"number"==typeof e.length)return a&&(e=a),l=0,{s:t=function(){},n:function(){return l>=e.length?{done:!0}:{done:!1,value:e[l++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$1(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$1(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$1(e,t):void 0}function _arrayLikeToArray$1(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ownKeys$a(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$a(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$a(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function GenericCheckbox(t){var e=_objectSpread$a({type:"",label:{position:"right"},column:4},t.style),n=useMemo(function(){var e=!1;return null!=t&&t.checked&&isObject(t.checked)&&isString(null==t?void 0:t.value)?e=t.checked[t.value]:isString(t.value)||(e=t.value),e},[null==t?void 0:t.checked,t.value]),e=classNames("wprf-checkbox-wrap",_defineProperty(_defineProperty(_defineProperty({},"wprf-".concat(null==e?void 0:e.type),0<(null==e?void 0:e.type.length)),"wprf-checked",Boolean(n)),"wprf-label-position-".concat(null==e||null==(n=e.label)?void 0:n.position),null==e||null==(n=e.label)?void 0:n.position),null==t?void 0:t.classes);return createElement("div",{className:e},createElement(GenericInput,_objectSpread$a(_objectSpread$a({},t),{},{type:"checkbox"})),createElement("label",{htmlFor:t.id},t.label))}function Checkbox(t){var n,r,o,e=t.options,l=t.value,a=t.multiple,i=t.style,u=sortingFields(e),c=_objectSpread$a({column:4},i);return a?(e=_slicedToArray(useState({}),2),n=e[0],r=e[1],o=function(e){var t=e.target||e.currentTarget;r(function(e){return _objectSpread$a(_objectSpread$a({},e),{},_defineProperty({},t.value,t.checked))})},useEffect(function(){t.onChange({target:{type:"checkbox",name:t.name,value:n,multiple:!0}})},[n]),useEffect(function(){if(isObject(l))r(l);else{var e,t={},n=_createForOfIteratorHelper$1(u);try{for(n.s();!(e=n.n()).done;)t[e.value.value]=l}catch(e){n.e(e)}finally{n.f()}r(t)}},[]),createElement("div",{className:"wprf-checkbox-wrapper wprf-control"},createElement(Row,null,u.map(function(e){return createElement(Column,{key:e.value,column:c.column},createElement(GenericCheckbox,_objectSpread$a(_objectSpread$a({},e),{},{context:null==t?void 0:t.context,id:e.value,checked:void 0===n[e.value]||(null!=n&&n[e.value]?l:!(null==n||!n[e.value])),type:"checkbox",onChange:o,style:c})))})))):createElement(GenericInput,_objectSpread$a(_objectSpread$a({},t),{},{type:"checkbox"}))}var Checkbox$1=withLabel(Checkbox),Field=function(e){if(!e.type||0===e.type.length)throw console.error(e),new Error(__("Field must have a #type. see documentation.","notificationx"));switch(e.type){case"text":case"radio":case"email":case"range":case"number":case"hidden":return createElement(Input$1,e);case"checkbox":return createElement(Checkbox$1,e);case"textarea":return createElement(Textarea$1,e);case"codeviewer":return createElement(CodeViewer$1,e);case"message":return createElement(Message,e);case"select":return createElement(Select$1,e);case"select-async":return createElement(SelectAsync$1,e);case"slider":return createElement(Slider,e);case"group":return createElement(Group$1,e);case"radio-card":return createElement(Radio,e);case"section":return createElement(Section$1,e);case"date":return createElement(Date$1,e);case"toggle":return createElement(Toggle,e);case"colorpicker":return createElement(ColorPicker$1,e);case"jsonuploader":return createElement(JsonUploader$1,e);case"repeater":return createElement(Repeater,e);case"media":return createElement(Media$1,e);case"editor":return createElement(Editor$1,e);case"action":return createElement(Action,e);case"button":return createElement(Button$1,e);case"modal":return createElement(Modal,e);case"tab":return createElement(Tab,e);case"responsive-number":return createElement(ResponsiveNumber$1,e);default:var t=applyFilters("custom_field","",e.type,e);return createElement(Fragment,null,t)}},GenericField=withProps(Field,!0),Field$1=withProps(Field),DateControl=function(e){var t=e.name,n=e.value,r=e.onChange,o=e.position,l=__experimentalGetSettings(),a=null!=(e=null==e?void 0:e.format)?e:l.formats.datetime,i=getTime(n),u=/a(?!\\)/i.test(l.formats.datetime.toLowerCase().replace(/\\\\/g,"").split("").reverse().join(""));return useEffect(function(){r({target:{type:"date",name:t,value:i}})},[]),createElement(Dropdown,{className:"wprf-control-datetime",contentClassName:"wprf-control-datetime-content",position:o||"bottom right",renderToggle:function(e){return e.isOpen,createElement(Button$2,{isTertiary:!0,onClick:e.onToggle},date(a,i,-(new Date).getTimezoneOffset()))},renderContent:function(){return createElement(DateTimePicker,{__nextRemoveHelpButton:!0,__nextRemoveResetButton:!0,currentDate:getTime(i).toDate().toString(),onChange:function(e){r({target:{type:"date",name:t,value:moment(e).utc().format()}})},is12Hour:u})}})},Date$1=withLabel(DateControl);function ownKeys$9(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$9(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$9(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$9(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Input=function(t,e){function n(e){return o.onChange(e,{popup:null==t?void 0:t.popup,isPro:!!t.is_pro,nx_has_permission:!!t.nx_has_permission,permission_popup:null==t?void 0:t.nx_has_permission})}var r=t.type||"text",o=validFieldProps(_objectSpread$9(_objectSpread$9({},t),{},{type:r}),["is_pro","nx_has_permission","visible","trigger","copyOnClick","disable","parentIndex","context","badge","popup","tags"]),r=useRef(null),e=null!=e&&e.current?e:r,l=useBuilderContext();"checkbox"===o.type&&null!=o&&o.name&&(o.checked=(null==o?void 0:o.checked)||(null==o?void 0:o.value));var a,r=_slicedToArray(useState(!1),2),i=r[0],u=r[1],c=(useEffect(function(){var e;return i&&(e=setTimeout(function(){u(!1)},2e3)),function(){return e&&clearTimeout(e)}},[i]),useCallback(function(e){e=null==e||null==(e=e.target)?void 0:e.getAttribute("data-num-sug");l.setFieldValue(o.name,e)},[o]));return!t.is_pro&&null!=t&&t.copyOnClick&&null!=t&&t.value?(r=(null==t?void 0:t.copyMessage)||"Click To Copy!",a=(null==t?void 0:t.copiedMessage)||"Copied!",createElement("span",{className:"wprf-clipboard-wrapper"},React.createElement("input",_objectSpread$9(_objectSpread$9({},o),{},{onChange:n})),createElement("span",{className:"wprf-clipboard-tooltip"},createElement("span",{className:"wprf-clipboard-tooltip-text"},i?a:r),createElement(Button$2,{className:"wprf-copy-icon",onClick:function(){copy(t.value,{format:"text/plain",onCopy:function(){u(!0)}})}},"Copy")))):createElement("span",null,React.createElement("input",_objectSpread$9(_objectSpread$9({},o),{},{onChange:n,ref:e})),(null==o?void 0:o.suggestions)&&0<(null==o?void 0:o.suggestions.length)&&createElement("div",{className:"wprf-num-suggestions"},null==o||null==(a=o.suggestions)?void 0:a.map(function(e,t){return createElement("span",{onClick:c,"data-num-sug":e.value},e.value+" "+e.unit)})))},GenericInput=React.memo(React.forwardRef(Input)),Input$1=withLabel(React.memo(Input));function ownKeys$8(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$8(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$8(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$8(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Textarea=function(t){var n=validFieldProps(t,["is_pro","visible","trigger","disable","parentIndex","context"]),e=useCallback(function(e){return n.onChange(e,{isPro:!!t.is_pro})},[null==n?void 0:n.value]);return React.createElement("textarea",_objectSpread$8(_objectSpread$8({},n),{},{onChange:e,rows:5}))},Textarea$1=withLabel(React.memo(Textarea));function ownKeys$7(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$7(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$7(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$7(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var CodeViewer=function(t){var n=validFieldProps(t,["is_pro","visible","trigger","disable","parentIndex","context","copyOnClick"]),e={onChange:useCallback(function(e){return n.onChange(e,{isPro:!!t.is_pro})},[null==n?void 0:n.value]),rows:5},r=(!t.is_pro&&null!=t&&t.copyOnClick&&null!=t&&t.value&&(e.onClick=function(){var e=null!=t&&t.success_text?t.success_text:__("Copied to Clipboard.","notificationx");copy(t.value,{format:"text/plain",onCopy:function(){t.context.alerts.toast("success",e)}})}),null!=t&&t.button_text?t.button_text:__("Click to Copy","notificationx"));return createElement("span",{className:"wprf-code-viewer"},React.createElement("textarea",_objectSpread$7(_objectSpread$7({},n),e)),createElement(Button$2,{className:"wprf-copy-button"},r))},CodeViewer$1=withLabel(React.memo(CodeViewer)),JsonUploader=function(n){validFieldProps(n,["is_pro","visible","trigger","disable","parentIndex","context","copyOnClick"]);var e=_slicedToArray(useState(),2),t=e[0],r=e[1];return useEffect(function(){null!=n&&n.value||r(null)},[null==n?void 0:n.value]),createElement("span",{className:"wprf-json-uploader"},!t&&createElement("label",{className:"wprf-json-uploaderButton"},createElement("span",null,__("Upload")),createElement("input",{type:"file",accept:"application/JSON",onChange:function(e){var t;(e=e).target.files.length&&(0==(null==(e=e.target.files[0])?void 0:e.size)?n.context.alerts.toast("error",__("File can't be empty.","notificationx")):"application/json"!=(null==e?void 0:e.type)&&"text/json"!=(null==e?void 0:e.type)?n.context.alerts.toast("error",__("Invalid file type.","notificationx")):(r(e),(t=new FileReader).onload=function(e){e=null==e||null==(e=e.target)?void 0:e.result;n.onChange({target:{type:"jsonuploader",name:n.name,value:e}})},t.readAsText(e)))}})),t&&(null==t?void 0:t.name)&&createElement("span",{className:"wpfr-json-file-name-wrapper"},createElement("span",{className:"wpfr-json-file-name"},20<(null==t?void 0:t.name.length)?"".concat(null==t?void 0:t.name.substr(0,9),"...").concat(null==t?void 0:t.name.substr((null==t?void 0:t.name.length)-7)):null==t?void 0:t.name),createElement("span",{className:"wprf-json-file-delete-button",onClick:function(){r(null),n.onChange({target:{type:"jsonuploader",name:n.name,value:null}})}},"x")))},JsonUploader$1=withLabel(React.memo(JsonUploader)),_excluded$2=["name","fields"],Group=function(n){var r,o,e,t,l=n.name,a=n.fields,i=_objectWithoutProperties(n,_excluded$2);if(a&&isArray(a)&&0!==a.length)return r=useBuilderContext(),o=useCallback(function(e){e.persist&&e.persist();var e=executeChange(e),t=e.field;r.setFieldValue([l,t],e.val)},[n.value]),e=sortingFields(a),useEffect(function(){r.setFormField([].concat(_toConsumableArray(n.parentIndex),["fields"]),e)},[]),a=e.map(function(e,t){t=[].concat(_toConsumableArray(n.parentIndex),["fields",t]);return createElement(GenericField,_extends$1({},i,{key:e.name,index:n.index,onChange:o},e,{parenttype:"group",parent:l,parentIndex:t}))}),t=classNames("wprf-group-control-inner",{"wprf-display-inline":"inline"===(null==n?void 0:n.display)}),createElement("div",{className:"wprf-group-control"},createElement("div",{className:t},a));throw new Error(__("You should give a #fields arguments to a group field.","notificationx"))},Group$1=withLabel(Group);function ownKeys$6(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$6(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$6(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$6(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Select=function(o){function e(){if(o.ajax&&(!o.ajax.rules||when(o.ajax.rules,l.values))){y(!0);var r={};if(Object.keys(null==o?void 0:o.ajax.data).map(function(e){var t,n;-1<(null==o?void 0:o.ajax.data[e].indexOf("@"))?(n=null==o?void 0:o.ajax.data[e].substr(1),r[e]=null==(t=l.values)?void 0:t[n]):r[e]=null==o?void 0:o.ajax.data[e]}),!g)return wpFetch({path:null==o?void 0:o.ajax.api,data:r}).then(function(e){y(!1);var t=merge(o.options,e,"value");return l.setFormField([].concat(_toConsumableArray(c),["options"]),t),f({options:t,parentIndex:[].concat(_toConsumableArray(c),["options"])}),e})}}var l=useBuilderContext(),t=o.id,n=o.name,r=o.multiple,a=o.placeholder,i=o.search,i=void 0!==i&&i,u=o.onChange,c=o.parentIndex,s=useOptions(o,"options"),d=s.options,p=s.selectedOption,f=s.setData,s=_slicedToArray(useState(null),2),m=s[0],v=s[1],s=_slicedToArray(useState(!1),2),b=s[0],y=s[1],s=_slicedToArray(useState(!1),2),g=s[0];return useEffect(function(){!isArray(m)&&isObject(m)&&u({target:{type:"select",name:n,value:m.value,options:d,multiple:r}}),isArray(m)&&u({target:{type:"select",name:n,value:m.map(function(e){return e.value}),options:d,multiple:r}})},[m]),useEffect(function(){e()},[]),useEffect(function(){null!=o&&o.menuOpen&&e()},[null==o?void 0:o.menuOpen]),createElement("div",{className:"wprf-select-wrapper"},createElement(ReactSelect,{isDisabled:null==o?void 0:o.disable,classNamePrefix:"wprf-select",isSearchable:null!=i&&i,id:t,name:n,isMulti:null!=r&&r,placeholder:a,isLoading:b,options:d,value:p,onMenuOpen:e,onMenuClose:function(){y(!1)},isOptionDisabled:function(e){return null==e?void 0:e.disabled},onChange:function(e){!isArray(e)&&isObject(e)?v(_objectSpread$6({},e)):isArray(e)?v(_toConsumableArray(e)):v(e)}}))},Select$1=withLabel(Select);let instanceMap=new WeakMap;function createId(e){var t=instanceMap.get(e)||0;return instanceMap.set(e,t+1),t}function useInstanceId(t,n,r){return useMemo(()=>{var e;return r||(e=createId(t),n?n+"-"+e:e)},[t])}var _RepeaterField=function(t){var e=useBuilderContext(),n=t.fields,r=t.onChange,o=t.index,l=t.parent,a=_slicedToArray(useState(t.isCollapsed),2),i=a[0],u=a[1],c=useInstanceId(_RepeaterField),a=null==(a=e.values)||null==(a=a[l])?void 0:a[o],a=(null==a?void 0:a.title)||(null==a?void 0:a.post_title)||(null==a?void 0:a.username)||(null==a?void 0:a.plugin_theme_name),a=a?a.length<40?a:a.substr(0,40)+"...":"";return useEffect(function(){e.setFieldValue([l,o,"isCollapsed"],i)},[i]),createElement("div",{className:"wprf-repeater-field"},createElement("div",{className:"wprf-repeater-field-title",onClick:function(){return u(!i)}},createElement("h4",null,createElement(Icon,{icon:"move"}),t.index+1,": ",a),createElement("div",{className:"wprf-repeater-field-controls"},createElement(Icon,{onClick:function(e){null!=e&&e.stopPropagation(),t.clone(t.index)},icon:"admin-page"}),createElement(Icon,{onClick:function(e){null!=e&&e.stopPropagation(),t.remove(t.index)},icon:"trash"}))),!i&&createElement("div",{className:"wprf-repeater-inner-field"},n.map(function(e,t){return createElement(GenericField,_extends$1({key:"field-".concat(o,"-").concat(t)},e,{id:"field-".concat(c,"-").concat(o,"-").concat(t),index:o,parenttype:"repeater",parent:l,onChange:function(e){return r(e,o)}}))})))};function ownKeys$5(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$5(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$5(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$5(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var GenericToggle=function(t){var e=_objectSpread$5({type:"",label:{position:"right"},column:4},t.style),n=useMemo(function(){var e=!1;return null!=t&&t.checked&&isObject(t.checked)&&isString(null==t?void 0:t.value)?e=t.checked[t.value]:isString(t.value)||(e=t.value),e},[null==t?void 0:t.checked,t.value]),e=classNames("wprf-toggle-wrap",_defineProperty(_defineProperty(_defineProperty({},"wprf-".concat(null==e?void 0:e.type),0<(null==e?void 0:e.type.length)),"wprf-checked",Boolean(n)),"wprf-label-position-".concat(null==e||null==(n=e.label)?void 0:n.position),null==e||null==(n=e.label)?void 0:n.position),null==t?void 0:t.classes);return createElement("div",{className:e},createElement(GenericInput,_objectSpread$5(_objectSpread$5({},t),{},{type:"checkbox",placeholder:void 0})),createElement(Label,{htmlFor:t.id}))},GenericToggle$1=withLabel(GenericToggle),ModalContent=function(n){var e=n.isLoading,t=n.closeModal,r=_slicedToArray(useState([]),2),o=r[0],l=r[1];return useEffect(function(){var e=sortingFields(n.body.fields).map(function(e,t){t=[].concat(_toConsumableArray(n.parentIndex),["fields",t]);return createElement(Field$1,_extends$1({key:e.name},e,{parentIndex:t}))});l(e)},[]),createElement("div",{className:"wprf-modal-body"},e&&createElement(Loading,null),!e&&createElement(Fragment,null,createElement("div",{className:"wprf-modal-content"},0<o.length&&o),createElement("div",{className:"wprf-modal-footer clearfix"},createElement("div",{className:"wprf-modal-footer-left"},(null==(r=n.body)?void 0:r.footer)&&isString(n.body.footer)&&createElement("p",null,n.body.footer),null==n||!n.confirm_button||null!=n&&null!=(e=n.confirm_button)&&e.close_action?"":createElement(GenericField,_extends$1({type:"button"},n.confirm_button)),null!=n&&n.confirm_button&&null!=n&&null!=(o=n.confirm_button)&&o.close_action?createElement(GenericField,{type:"button",onClick:t,text:null==n||null==(r=n.confirm_button)?void 0:r.text}):""))))},ModalHeader=function(e){e=e.content;return createElement("div",{className:"wprf-modal-header"},e&&isString(e)&&createElement("h3",null,e))},Loading=function(e){return createElement("p",null,__("Loading...","notificationx"))},toolbarOptions={options:["inline","blockType","textAlign","colorPicker","link"],inline:{options:["bold","italic","underline","strikethrough","monospace"]},blockType:{inDropdown:!0,options:["Normal","H1","H2","H3","H4","H5","H6","Blockquote","Code"],className:void 0,component:void 0,dropdownClassName:void 0}};function _createForOfIteratorHelper(e,t){var n,r,o,l,a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return o=!(r=!0),{s:function(){a=a.call(e)},n:function(){var e=a.next();return r=e.done,e},e:function(e){o=!0,n=e},f:function(){try{r||null==a.return||a.return()}finally{if(o)throw n}}};if(Array.isArray(e)||(a=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length)return a&&(e=a),l=0,{s:t=function(){},n:function(){return l>=e.length?{done:!0}:{done:!1,value:e[l++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ownKeys$4(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$4(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$4(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$4(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Toggle=function(t){var n,r,o,e=t.options,l=t.value,a=t.multiple,i=t.style,u=sortingFields(e),c=_objectSpread$4({column:4},i);return a?(e=_slicedToArray(useState({}),2),n=e[0],r=e[1],o=function(e){var t=e.target||e.currentTarget;r(function(e){return _objectSpread$4(_objectSpread$4({},e),{},_defineProperty({},t.value,t.checked))})},useEffect(function(){t.onChange({target:{type:"toggle",name:t.name,value:n}})},[n]),useEffect(function(){if(isObject(l))r(l);else{var e,t={},n=_createForOfIteratorHelper(u);try{for(n.s();!(e=n.n()).done;)t[e.value.value]=l}catch(e){n.e(e)}finally{n.f()}r(t)}},[]),createElement("div",{className:"wprf-toggle-wrapper wprf-control"},createElement(Row,null,u.map(function(e){return createElement(Column,{key:e.value,column:c.column},createElement(GenericToggle$1,_objectSpread$4(_objectSpread$4({},e),{},{context:null==t?void 0:t.context,id:e.value,checked:void 0===n[e.value]||(null!=n&&n[e.value]?l:!(null==n||!n[e.value])),type:"checkbox",onChange:o,style:c})))})))):createElement(GenericToggle$1,t)},_excluded$1=["label","value","icon","is_pro"];function ownKeys$3(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$3(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$3(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$3(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var getRandomValues,_RadioCard=function(e){var i,u,c,s=useBuilderContext(),t=useOptions(e,"options"),n=t.options,d=t.option;if(n)return i=useInstanceId(_RadioCard),t=classNames(["wprf-control","wprf-radio-card","wprf-input-radio-set-wrap",null==e?void 0:e.className]),u=_objectSpread$3({},null==e?void 0:e.style),c=validFieldProps(e,["options","placeholder","style","trigger"]),useEffect(function(){d&&e.onChange({target:{type:"radio-card",name:e.name,value:d}})},[d]),createElement("div",{className:t},createElement(Row,null,n.map(function(e,t){var n,r=e.label,o=e.value,l=e.icon,a=e.is_pro,e=_objectWithoutProperties(e,_excluded$1);return createElement(Column,{column:+(null==e?void 0:e.column)||4,key:t},createElement("div",{className:classNames("wprf-input-radio-option",{"wprf-option-has-image":null!=l&&l,"wprf-option-selected":o==d})},createElement(Label,{className:classNames(_defineProperty({"wprf-label-has-image":null!=l&&l},"wprf-size-".concat(u.size),null!=(n=l&&(null==u?void 0:u.size))&&n)),htmlFor:"wprf-input-radio-".concat(i,"-").concat(t),src:l,badge:{label:a?"Pro":"Free",value:a,active:Boolean(s.is_pro_active)}},r),createElement(GenericInput,_extends$1({},e,c,{is_pro:a,type:"radio",value:o,checked:o===d,id:"wprf-input-radio-".concat(i,"-").concat(t)}))))})));throw new Error(__("#options is a required arguments for RadioCard field.","notificationx"))},Radio=withLabel(_RadioCard),Section=function(n){var t=useBuilderContext(),e=_slicedToArray(useState(null!=(e=n.collapsed)&&e),2),r=e[0],o=e[1],e=_slicedToArray(useState([]),2),l=e[0],a=e[1],e=(useEffect(function(){var e=sortingFields(n.fields),e=(t.setFormField([].concat(_toConsumableArray(n.parentIndex),["fields"]),e),e.map(function(e,t){t=[].concat(_toConsumableArray(n.parentIndex),["fields",t]);return createElement(Field$1,_extends$1({key:e.name},e,{parentIndex:t}))}));a(e)},[]),classNames("wprf-control-section",null==n?void 0:n.classes,null==n?void 0:n.name,{"wprf-section-collapsed":(null==n?void 0:n.collapsible)&&r}));return createElement("div",{id:null==n?void 0:n.name,className:e},n.placeholder&&createElement("div",{className:"wprf-section-title"},createElement("h4",null,n.placeholder),n.collapsible&&createElement("button",{onClick:function(){return o(!r)}},"Icon")),createElement("div",{className:"wprf-section-fields"},l))},Section$1=React.memo(Section),rnds8=new Uint8Array(16);function rng(){if(getRandomValues=getRandomValues||"undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto))return getRandomValues(rnds8);throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported")}var REGEX=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function validate(e){return"string"==typeof e&&REGEX.test(e)}for(var byteToHex=[],i=0;i<256;++i)byteToHex.push((i+256).toString(16).substr(1));function stringify(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,e=(byteToHex[e[t+0]]+byteToHex[e[t+1]]+byteToHex[e[t+2]]+byteToHex[e[t+3]]+"-"+byteToHex[e[t+4]]+byteToHex[e[t+5]]+"-"+byteToHex[e[t+6]]+byteToHex[e[t+7]]+"-"+byteToHex[e[t+8]]+byteToHex[e[t+9]]+"-"+byteToHex[e[t+10]]+byteToHex[e[t+11]]+byteToHex[e[t+12]]+byteToHex[e[t+13]]+byteToHex[e[t+14]]+byteToHex[e[t+15]]).toLowerCase();if(validate(e))return e;throw TypeError("Stringified UUID is invalid")}function v4(e,t,n){var r=(e=e||{}).random||(e.rng||rng)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return stringify(r)}function ownKeys$2(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$2(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$2(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$2(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Repeater=function(e){var o=e.name,t=e.button,n=e.fields,l=useBuilderContext(),e=_slicedToArray(useState(null==(e=l.values)?void 0:e[o]),2),r=e[0],a=e[1],i=(useEffect(function(){var e;null!=(null==(e=l.values)?void 0:e[o])&&a(null==(e=l.values)?void 0:e[o])},[null==(e=l.values)?void 0:e[o]]),useCallback(function(e){var t=_toConsumableArray(r);t.splice(e,1),l.setFieldValue(o,t)},[r])),u=useCallback(function(e){var t=_toConsumableArray(r);0<t.length&&(null!=(e=t=null!=(e=t=null!=(e=t=null!=(e=t=(null==t?void 0:t[e])||{})&&e.title?_objectSpread$2(_objectSpread$2({},t),{},{title:t.title+" - Copy"}):t)&&e.post_title?_objectSpread$2(_objectSpread$2({},t),{},{post_title:t.post_title+" - Copy"}):t)&&e.username?_objectSpread$2(_objectSpread$2({},t),{},{username:t.username+" - Copy"}):t)&&e.plugin_theme_name&&(t=_objectSpread$2(_objectSpread$2({},t),{},{plugin_theme_name:t.plugin_theme_name+" - Copy"})),t=_objectSpread$2(_objectSpread$2({},t),{},{index:v4(),isCollapsed:!1}),l.setFieldValue([o,r.length],t))},[r]);return useEffect(function(){a(null==r||""==r?[{index:v4()}]:function(e){return e.map(function(e){return _objectSpread$2(_objectSpread$2({},e),{},{index:v4()})})})},[]),createElement("div",{className:"wprf-repeater-control"},r&&0<(null==r?void 0:r.length)&&createElement(ReactSortable,{className:"wprf-repeater-content",list:r,setList:function(e){l.setFieldValue(o,e)},handle:".wprf-repeater-field-title",filter:".wprf-repeater-field-controls",forceFallback:!0},r.map(function(e,r){return createElement(_RepeaterField,{isCollapsed:null==e?void 0:e.isCollapsed,key:(null==e?void 0:e.index)||r,fields:n,index:r,parent:o,clone:u,remove:i,onChange:function(e){var t,n;t=r,(e=e).persist&&e.persist(),e=executeChange(e),n=e.field,l.setFieldValue([o,t,n],e.val)}})})),createElement("div",{className:"wprf-repeater-label"},createElement("button",{className:"wprf-repeater-button",onClick:function(){return l.setFieldValue(o,[].concat(_toConsumableArray(r),[{index:v4()}]))}},null==t?void 0:t.label)))},Slider=function(t){var n=t.name,e=t.id,r=t.label,o=t.units,l=t.min,a=t.max,i=t.unit,u=t.reset,c=_slicedToArray(useState(t.value||0),2),s=c[0],d=c[1],c=_slicedToArray(useState(i),2),p=c[0],f=c[1];return useEffect(function(){var e;s&&(isNumber(s)?e=p?"".concat(s).concat(p):"".concat(s):isString(s)&&(e=-1<s.indexOf(p)?"".concat(s):"".concat(s).concat(p)),t.onChange({target:{type:"slider",name:n,value:e}}))},[s,p]),createElement("div",{className:"wprf-slider-wrap"},createElement("div",{className:"wprf-slider-control-head"},createElement(Label,{htmlFor:e||n},r),isArray(o)&&0<o.length&&createElement("div",{className:"wprf-slider-units"},o.map(function(e,t){return createElement(Button$2,{key:t,isSmall:!0,isPrimary:!0,onClick:function(){return f(e)},className:e==p?"unit-active":""},e)}))),createElement("div",{className:"wprf-slider-control"},createElement(RangeControl,{allowReset:null==u||u,value:parseInt(s),min:l,max:a,onChange:function(e){return d(e)}})))},SelectAsync=function(o){function l(t,n){var e;if(o.ajax&&(!o.ajax.rules||when(o.ajax.rules,a.values)))if(t)if(t.length<3)n([{label:"Please type 3 or more characters.",value:null,disabled:!0}]);else{var r={inputValue:t};if(null!=(e=Object.keys(o.ajax.data))&&e.map(function(e){var t,n;-1<o.ajax.data[e].indexOf("@")?(n=o.ajax.data[e].substr(1),r[e]=null==(t=a.values)?void 0:t[n]):r[e]=o.ajax.data[e]}),!f&&t)return m(!0),window.lastRequest=null,wpFetch({path:o.ajax.api,data:r}).then(function(e){return n(e),e}).finally(function(){var e;m(!1),window.lastRequest&&(e=window.lastRequest,window.lastRequest=null,l.apply(void 0,_toConsumableArray(e))),window.lastCompleteRequest=t});window.lastRequest=[t,n]}else n(c)}var a=useBuilderContext(),e=o.id,t=o.name,n=o.multiple,r=o.placeholder,i=o.onChange,u=_slicedToArray(useState(a.eligibleOptions(o.options)),2),c=u[0],s=u[1],u=_slicedToArray(useState(null==o?void 0:o.value),2),d=u[0],p=u[1],u=_slicedToArray(useState(!1),2),f=u[0],m=u[1];return useEffect(function(){s(a.eligibleOptions(o.options))},[a.values.source]),useEffect(function(){i({target:{type:"select",name:t,value:d,multiple:n}})},[d]),createElement("div",{className:"wprf-async-select-wrapper"},createElement(AsyncSelect,{cacheOptions:!0,loadOptions:l,defaultOptions:c,isDisabled:null==o?void 0:o.disable,isMulti:null!=n&&n,classNamePrefix:"wprf-async-select",id:e,name:t,placeholder:r,formatOptionLabel:function(e,t){var n,r;if(null!=t&&null!=(n=t.inputValue)&&n.length&&e.name&&e.name.toLowerCase().includes(null==t||null==(n=t.inputValue)?void 0:n.toLowerCase()))return null!=e&&e.name,n=new RegExp("(".concat(null==t?void 0:t.inputValue,")"),"gi"),t=null==(t=e.name)?void 0:t.replace(n,"<strong style={font-weight: 900}>$1</strong>"),r=null==(r=e.address)?void 0:r.replace(n,"<strong style={font-weight: 900}>$1</strong>"),createElement(Fragment,null,parse(t||"")," ",createElement("small",null,parse(r||"")));return createElement(Fragment,null,e.name?createElement(Fragment,null,createElement("b",null,e.name)," "):createElement(Fragment,null,e.label," "),e.address&&createElement("small",null,e.address))},value:d,isClearable:!0,isOptionDisabled:function(e){return null==e?void 0:e.disabled},onChange:function(e){return p(e)}}))},SelectAsync$1=withLabel(SelectAsync),ColorPicker=function(e){var t,n=e.value,r=e.name,o=e.id,l=e.onChange,e=_slicedToArray(useState(!1),2),a=e[0],i=e[1],e=_slicedToArray(useState(n||null),2),u=e[0],c=e[1],e=_slicedToArray(useState(null),2),s=e[0],d=e[1],e=useRef(null);useEffect(function(){d(n)},[]);return useEffect(function(){l({target:{type:"colorpicker",name:r,value:u}})},[u]),useEffect(function(){function e(e){t.current&&!t.current.contains(e.target)&&i(!1)}return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}},[t=e]),createElement(Fragment,null,createElement("div",{className:"wprf-colorpicker-wrap",ref:e},createElement("input",{type:"hidden",value:n,name:r,id:o}),createElement("span",{className:"wprf-picker-display",style:{backgroundColor:n,borderColor:n},onClick:function(){return i(!a)}}),a&&createElement(Fragment,null,createElement("button",{className:"wprf-colorpicker-reset",onClick:function(e){e.preventDefault(),c(s),i(!1)}},__("Reset","notificationx")),createElement(ColorPicker$2,{color:n,onChangeComplete:function(e){return c(e.hex)}}))))},ColorPicker$1=withLabel(ColorPicker),Action=function(e){return createElement(Fragment,null,applyFilters(e.action,"",e))},Media=function(n){var e=_slicedToArray(useState(null!=(e=n.value)&&e.url?n.value:null),2),r=e[0],o=e[1],l=useBuilderContext();return useEffect(function(){n.onChange({target:{type:"media",name:n.name,value:r}})},[r]),createElement("div",{className:"wprf-control wprf-media"},null!=r&&!(null!=n&&n.notImage)&&createElement("div",{className:"wprf-image-preview"},null!=r&&(null==r?void 0:r.url)&&createElement("img",{src:r.url,alt:r.title})),createElement("div",{className:"wprf-image-uploader"},createElement(MediaUpload,{onSelect:function(e){o({id:e.id,title:e.title,url:e.url})},multiple:!1,allowedTypes:["image"],value:r,render:function(e){var t=e.open;return createElement(Fragment,null,null!=r&&createElement("button",{className:"wprf-btn wprf-image-remove-btn",onClick:function(){null!=n&&n.is_pro&&!l.is_pro_active?l.alerts.pro_alert(null==n?void 0:n.popup).fire():o(null)}},(null==n?void 0:n.remove)||"Remove"),createElement("button",{className:"wprf-btn wprf-image-upload-btn",onClick:function(){null!=n&&n.is_pro&&!l.is_pro_active?l.alerts.pro_alert(null==n?void 0:n.popup).fire():t()}},null!=r?(null==n?void 0:n.reset)||"Change Image":(null==n?void 0:n.button)||"Upload"))}})))},Media$1=withLabel(Media),Editor=function(n){var e=_slicedToArray(useState(EditorState.createEmpty()),2),t=e[0],r=e[1];return useEffect(function(){var e,t;n.value&&(e=(t=htmlToDraft(n.value)).contentBlocks,e=ContentState.createFromBlockArray(e,t.entityMap),t=EditorState.createWithContent(e),r(t))},[]),useEffect(function(){var e=draftToHtml(convertToRaw(t.getCurrentContent()));n.onChange({target:{type:"editor",value:e,name:n.name}})},[t]),createElement(Editor$2,{placeholder:null==n?void 0:n.placeholder,toolbar:toolbarOptions,editorState:t,toolbarClassName:"wprf-editor-toolbar",wrapperClassName:"wprf-editor wprf-control",editorClassName:"wprf-editor-main",onEditorStateChange:r})},Editor$1=withLabel(Editor),Button=function(n){var e,t,r,o;if(null!=n&&n.text||!0===(null==n?void 0:n.group))return e=validFieldProps(n,["is_pro","visible","disable","parentIndex","context","onBlur","value","ajax","text"]),t=(o=_slicedToArray(useState(!1),2))[0],r=o[1],null!=n&&n.href?createElement("a",{href:-1===(null==n?void 0:n.href)?null==n?void 0:n.value:null==n?void 0:n.href,target:null==n?void 0:n.target,className:classNames("wprf-control wprf-button wprf-href-btn",null==n?void 0:n.classes)},null==n?void 0:n.text):null!=n&&n.group?(o=n.fields.map(function(e,t){t=[].concat(_toConsumableArray(n.parentIndex),["fields",t]);return createElement(Field$1,_extends$1({key:e.name},e,{parentIndex:t}))}),createElement("div",{className:"wprf-control wprf-button-group wprf-flex"},o)):createElement(Fragment,null,createElement("button",_extends$1({},e,{name:n.name,disabled:t,onClick:null!=(o=null==n?void 0:n.onClick)?o:function(e){null!=n&&n.ajax&&(r(!0),hitAAJX(n.ajax,n.context).then(function(e){var t;if(r(!1),"error"==(null==e?void 0:e.status))throw new Error(null==e?void 0:e.message);n.onChange({target:{type:"button",name:n.name,value:!0}}),null!=(e=n.ajax)&&e.hideSwal||(e=(null==(e=n.ajax)||null==(e=e.swal)?void 0:e.icon)||"success",t=(null==(t=n.ajax)||null==(t=t.swal)?void 0:t.text)||"Complete",n.context.alerts.toast(e,t,{autoClose:null==(e=n.ajax)||null==(e=e.swal)?void 0:e.autoClose})),null!=(t=n.ajax)&&t.reload&&setTimeout(function(){return window.location.reload()},1e3)}).catch(function(e){var t;console.error("Error In Button Called",n.name,e),r(!1),n.onChange({target:{type:"button",name:n.name,value:!1}}),null!=(t=n.ajax)&&t.hideSwal||n.context.alerts.toast("error",(null==e?void 0:e.message)||__("Something went wrong.","notificationx"))})),useTrigger(n)},className:classNames("wprf-control wprf-button wprf-btn",null==n?void 0:n.classes)}),(null==n?void 0:n.icon)&&(isObject(n.icon)?null==n||null==(e=n.context)||null==(e=e.icons)||null==(e=e[null==n||null==(o=n.icon)?void 0:o.type])?void 0:e[null==n||null==(o=n.icon)?void 0:o.name]:""),isObject(null==n?void 0:n.text)&&null!=n&&n.ajax?t?null==n||null==(e=n.text)?void 0:e.loading:n.value?null==n||null==(o=n.text)?void 0:o.saved:null==n||null==(t=n.text)?void 0:t.normal:null==n?void 0:n.text));throw new Error(__("Button has a required params #text.","notificationx"))},Button$1=withLabel(Button);function ownKeys$1(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread$1(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys$1(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys$1(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var ResponsiveNumber=function(t){var n=validFieldProps(t,["is_pro","visible","trigger","disable","parentIndex","context","badge","popup"]),e=_slicedToArray(useState(Object.keys(t.controls)[0]),2),r=e[0],o=e[1],e=n.value;isObject(n.value)||Object.keys(t.controls).reduce(function(e,t){return _objectSpread$1(_objectSpread$1({},e),{},_defineProperty({},t,n.value))},{});var e=_slicedToArray(useState(e),2),l=e[0],a=e[1];return useEffect(function(){n.onChange({target:{type:"input",name:n.name,value:l,checked:null,multiple:null}})},[l]),createElement("div",{style:{display:"flex",alignItems:"center",rowGap:5,columnGap:10,flexWrap:"wrap"}},React.createElement("input",_objectSpread$1(_objectSpread$1({},n),{},{type:"number",value:null==l?void 0:l[r],onChange:function(e){a(_objectSpread$1(_objectSpread$1({},l),{},_defineProperty({},r,e.target.value)))}})),createElement("div",{style:{display:"flex",alignItems:"center"}},null==(e=Object.keys(t.controls))?void 0:e.map(function(e){return createElement("button",{type:"button",key:e,className:"responsive-button ".concat(r===e?"active":""),onClick:function(){return o(e)}},createElement("img",{src:t.controls[e].icon,alt:"desktop",style:{width:t.controls[e].size}}))})))},ResponsiveNumber$1=(ResponsiveNumber.defaultProps={type:"number"},withLabel(React.memo(ResponsiveNumber))),eligibleMessage=function(e){if(null!=e&&e.messages)for(var t in e.messages){t=e.messages[t];if(when(t.rules,e.context.values))return t}return{message:null==e?void 0:e.message,html:null==e?void 0:e.html,type:"normal"}},Message=function(e){var t=eligibleMessage(e),n=t.html,r=t.message,t=t.type;return r?createElement("div",{className:classNames("wprf-control","wprf-message","wprf-".concat(void 0===t?"warning":t,"-message"),"wprf-".concat(e.name,"-message"),null==e?void 0:e.classes)},n&&createElement("p",{dangerouslySetInnerHTML:{__html:r}}),!n&&createElement("p",null,r)):createElement(Fragment,null)},Modal=function(n){var e;if(null==(null==n?void 0:n.body)||null==(null==n?void 0:n.button))throw new Error(__("Modal needs button/body with it.","notificationx"));function r(){return i(!0)}function t(){return i(!1)}function o(){var e=null==(e=n.context.values)?void 0:e[n.cancel];null!=n&&n.cancel&&e&&e!==s&&t()}var l=_slicedToArray(useState(!1),2),a=l[0],i=l[1],l=_slicedToArray(useState(!1),2),l=l[0],u=useCallback(function(){},[]),c=useRef(),s=(useEffect(function(){var e;c.current=null==(e=n.context.values)?void 0:e[n.cancel]}),c.current);return createElement("div",{className:"wprf-control wprf-modal",id:"wprf-modal-".concat(n.name)},!(null!=n&&n.close_on_body)&&createElement(GenericField,_extends$1({type:"button"},null==n?void 0:n.button,{onClick:r})),(null==n?void 0:n.show_body)&&createElement("div",{className:"wprf-control wprf-modal-show-body"},null==n||null==(e=n.body)||null==(e=e.fields)?void 0:e.map(function(e){var t;return"text"===e.type?createElement("div",{className:"wprf-control wprf-modal-body-value-heading"},createElement("h4",{key:e.name},(null==(t=n.context.values)?void 0:t[e.name])||(null==e?void 0:e.default)),(null==n?void 0:n.close_on_body)&&createElement(GenericField,_extends$1({type:"button"},null==n?void 0:n.button,{onClick:r}))):"textarea"===e.type?createElement("p",{key:e.name},(null==(t=n.context.values)?void 0:t[e.name])||(null==e?void 0:e.default)):null})),a&&createElement(SweetAlert$1,{customClass:"wprf-modal-inner",style:{maxWidth:"900px",width:"100%",overflowY:"scroll",margin:"50px auto"},closeBtnStyle:{top:"5px",right:"5px",color:"#f78c8c",fontSize:"18px",border:"1px solid #f78c8c",borderRadius:"50%",width:"30px",height:"30px",display:"inline-flex",alignItems:"center",justifyContent:"center"},title:createElement(ModalHeader,{content:null==n||null==(e=n.body)?void 0:e.header}),onConfirm:u,showConfirm:!1,showCloseButton:!0,closeOnClickOutside:!0,onCancel:t,afterUpdate:function(){return o}},createElement(ModalContent,_extends$1({},n,{isLoading:l,closeModal:t,context:n.context,onConfirm:u}))))},InnerContent=function(e){var t=e.fields,r=e.parentIndex,n=e.context,e=_slicedToArray(useState([]),2),o=e[0],l=e[1],e=_slicedToArray(useState([]),2),a=e[0],i=e[1];return useEffect(function(){var e=sortingFields(t);n.setFormField([r,"fields"],e),l(e)},[]),useEffect(function(){var e;isArray(o)&&0<o.length&&(e=o.map(function(e,t){var n=[].concat(_toConsumableArray(r),["fields",t]);return"section"===(null==e?void 0:e.type)?createElement(GenericField,_extends$1({key:"input-".concat(e.name,"-").concat(t)},e,{parentIndex:n})):e?createElement(Field$1,_extends$1({key:"input-".concat(e.name,"-").concat(t)},e,{parentIndex:n})):createElement(Fragment,null)}),i(e))},[o]),createElement(Fragment,null,a)};function _objectDestructuringEmpty(e){if(null==e)throw new TypeError("Cannot destructure "+e)}var Submit=function(e){var e=_extends$1({},(_objectDestructuringEmpty(e),e)),n=useBuilderContext(),e=(null==e?void 0:e.label)||__("Save Changes","notificationx"),t=useCallback(function(e){var t;null!=(t=n.submit)&&t.onSubmit&&n.submit.onSubmit(e,n)},[n]);return createElement("div",{className:"wprf-submit wprf-control"},createElement(Button$2,{className:"wprf-submit-button",onClick:t},e))},SteppedButton=function(n){var e=_slicedToArray(useState(void 0),2),r=e[0],o=e[1],e=_slicedToArray(useState(void 0),2),l=e[0],a=e[1];return useBuilderContext(),useEffect(function(){var e=n.fields.map(function(e){return e.id}),t=e.findIndex(function(e){return e===n.active});-1!=t&&a(e[t-1]),t<=e.length&&o(e[t+1])},[n.active,n.fields]),createElement("div",{className:"wprf-stepped-button"},n.config.buttons&&Object.keys(n.config.buttons).map(function(e,t){return createElement(React.Fragment,{key:"button_".concat(e,"_").concat(t)},("next"===e&&void 0!==r||"prev"===e&&void 0!==l)&&createElement(Button$2,{className:"wprf-btn wprf-step-btn-".concat(e),onClick:function(){return n.setActive("next"===e?r:l)}},null==(t=n.config.buttons)?void 0:t[e]),null==r&&(null==(t=n.config.buttons)||null==(t=t[e])?void 0:t.type)&&createElement(Field$1,null==(t=n.config.buttons)?void 0:t[e]))}))},SteppedButton$1=React.memo(SteppedButton),_excluded=["fields","active","setActive","submit"],Content=function(e){var t=e.fields,r=e.active,n=e.setActive,o=e.submit,l=_objectWithoutProperties(e,_excluded);if(void 0===t)throw new Error(__("There are no #tabs args defined in props.","notificationx"));var a,i,u=useBuilderContext(),c=l.parentIndex||[];if(isArray(t))return a=(e=_slicedToArray(useState([]),2))[0],i=e[1],useEffect(function(){var e=t.filter(function(e){return isVisible(null==u?void 0:u.values,e)});i(e)},[t,null==u||null==(e=u.values)?void 0:e.source]),createElement("div",{className:classNames("wprf-tab-content-wrapper",null==u||null==(e=u.values)?void 0:e.source,null==u||null==(e=u.values)?void 0:e.themes)},createElement("div",{className:"wprf-tab-flex"},createElement("div",{className:"wprf-tab-contents"},t.map(function(e,t){var n;return isVisible(null==u?void 0:u.values,e)?(n=classNames("wprf-tab-content","wprf-tab-".concat(null==e?void 0:e.id),{"wprf-active":r===e.id}),createElement("div",{id:null==e?void 0:e.id,className:n,key:null==e?void 0:e.id},((null==e?void 0:e.label)&&(null==(n=null==l?void 0:l.title)||n)||(null==l?void 0:l.content_heading))&&createElement("div",{className:"wprf-tab-heading-wrapper"},(null==e?void 0:e.label)&&(null==(n=null==l?void 0:l.title)||n)&&createElement("h4",null,e.label),createElement("div",null,(null==l?void 0:l.content_heading)&&Object.keys(l.content_heading).map(function(e,t){return createElement(React.Fragment,{key:"button_".concat(e,"_").concat(t)},createElement(Field$1,l.content_heading[e]))}))),createElement(InnerContent,{context:u,fields:null==e?void 0:e.fields,parentIndex:[].concat(_toConsumableArray(c),[t])}))):""})),applyFilters("wprf_tab_content","",l)),(null==l||null==(e=l.step)?void 0:e.show)&&createElement(SteppedButton$1,{fields:a,active:r,setActive:n,config:null!=(e=l.step)?e:{show:!1}}),(null==(a=null==o?void 0:o.show)||a)&&(null==o||!o.rules||when(null==o?void 0:o.rules,{rest:l}))&&createElement(Submit,o));throw new Error(__("Not an array.","notificationx"))},Tab=function(e){var t=useBuilderContext(),n=_slicedToArray(useState(e.value||e.active),2),r=n[0],o=n[1],n=classNames("wp-react-form wprf-tabs-wrapper",null==e?void 0:e.className,{"wprf-tab-menu-as-sidebar":null==e?void 0:e.sidebar});return useEffect(function(){e.value!==r&&o(e.value)},[e.value]),useEffect(function(){e.value!==r&&e.onChange({target:{type:"button",name:e.name,value:r}})},[r]),createElement("div",{className:n},createElement(Menu,_extends$1({},e,{active:r,setActive:function(e){return o(e)},fields:e.fields,context:t})),createElement(Content,_extends$1({},e,{fields:e.fields,active:r,setActive:function(e){return o(e)},submit:null==e?void 0:e.submit})))};function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}registerStore("formbuilder",store);var FormBuilder=function(e){var t,n=useBuilderContext(),r=e;return null!=(t=r)&&t.type||(r=_objectSpread(_objectSpread(_objectSpread({},e),e.config),{},{value:e.config.active,fields:e.tabs,tabs:void 0,submit:null==e?void 0:e.submit,onChange:function(e){n.setActiveTab(null==e||null==(e=e.target)?void 0:e.value)}})),createElement(Fragment,null,createElement(Tab,r))};export{Action,BuilderConsumer,BuilderProvider,Button$1 as Button,CodeViewer$1 as CodeViewer,ColorPicker$1 as ColorPicker,Column,Date$1 as Date,Editor$1 as Editor,Field$1 as Field,FormBuilder,GenericField,GenericInput,Group$1 as Group,Image,Input$1 as Input,JsonUploader$1 as JsonUploader,Label,Media$1 as Media,Message,Modal,ObjectFilter,Radio,Repeater,ResponsiveNumber$1 as ResponsiveNumber,Row,Section$1 as Section,Select$1 as Select,SelectAsync$1 as SelectAsync,Slider,SweetAlert,Textarea$1 as Textarea,Toggle,_extends,builderReducer,downloadFile,executeChange,getIn,getSelectedValues,getStoreData,getTime,hitAAJX,isArray,isEmptyObj,isExists,isFunction,isNumber,isObject,isString,isVisible,merge,objectWithoutPropertiesLoose,processAjaxData,setIn,setStoreData,sortingFields,triggerDefaults,useBuilder,useBuilderContext,useDefaults,validFieldProps,when,withLabel,withProps,withState,wpFetch};
