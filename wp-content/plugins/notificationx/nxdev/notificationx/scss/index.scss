// HEADER for NX
@import "./header";

@import "./build_with_ai";
// Loader CSS
@import "./loader";
// Analytics Card in Admin List Page
@import "./analytics_header";

@import "./analytics";
// NotificationX Items
@import "./notificationx_items";
// Create Nx
@import "./create_popup";
// Settings
@import "./settings";
// FlashingMessageIcon
@import "./flashingMessage";
@import './nx_new/daterange';

body {
    background-color: #f6f7fe;
}

// Alerts
@import "./nx_new/alerts";

.notificationx-admin {
    position: relative;
    z-index: 11;
}

#notificationx {
    @import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");

    @media only screen and (min-width: 1399.98px) {
        padding: 0px 10px;
    }

    font-family: "DM Sans",
    sans-serif;
    // HEADER for NX
    @import "./nx_new/header";
    // Analytics Card in Admin List Page
    @import "./nx_new/analytics_header";
    // NotificationX Items
    @import "./nx_new/notificationx_items";
    // Create Nx
    @import "./nx_new/create_popup";
    // toast Nx
    @import "./nx_new/toast";
    // NotificationX Add Item
    @import "./nx_new/notificationx_add_item";
    // NotificationX Form Builder
    @import "./nx_new/notificationx_form_builder";
    // Settings
    @import "./nx_new/settings";
    // datePicker
    @import "./nx_new/quick_builder";
    // newAdmin
    @import "./nx_new/new_admin";
    // cookie manager
    @import "./nx_new/cookie_manager";

    a:focus {
        outline: 0;
        box-shadow: none;
    }

    .pressbar-gutenberg-theme-popup {
        .swal2-actions {
            gap: 12px;

            .swal2-styled {
                background: #6A4BFF;
                border: 1px solid #6A4BFF;
                Margin: 0;
                transition-duration: .05s;
                transition-timing-function: ease-in-out;

                a {
                    color: #FFFFFF;
                    text-decoration: none;
                    transition-duration: .05s;
                    transition-timing-function: ease-in-out;

                    :hover {
                        color: #FFFFFF;
                    }
                }
            }

            :hover {
                background: #5414D0;
                box-shadow: rgba(86, 20, 213, 0.3) 0px 15px 25px -5px;
            }

            :focus {
                box-shadow: none;
            }
        }
    }
}

@import "./modal";
@import "./nx_new/datePicker";
@import "./nx_new/_cookies_list";

.nx-announcement-entries {
    >.wprf-section-fields {
        padding: 0px !important;
    }
}