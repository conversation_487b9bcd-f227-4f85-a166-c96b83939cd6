import React, { useState, useCallback } from 'react'
import { __ } from '@wordpress/i18n'
import { useNotificationXContext } from '../hooks'

interface PresetDesign {
  id: string
  title: string
  description: string
  preview_url: string
  theme_data: any
  elementor_data?: any
  gutenberg_data?: any
  style: 'modern' | 'classic' | 'minimal' | 'bold' | 'gradient'
  colors: {
    primary: string
    secondary: string
    text: string
    background: string
  }
}

const BuildWithAI = () => {
  const { assets } = useNotificationXContext()
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedPresets, setGeneratedPresets] = useState<PresetDesign[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMorePresets, setHasMorePresets] = useState(true)
  // const [selectedPreset, setSelectedPreset] = useState<PresetDesign | null>(null) // For future use

  // Sample preset data - In real implementation, this would come from AI API
  const samplePresets: PresetDesign[] = [
    {
      id: 'ai-preset-1',
      title: 'Modern Sale Banner',
      description: 'Clean modern design with gradient background',
      preview_url: `${assets.admin}/images/extensions/themes/bar-elementor/theme-one.jpg`,
      theme_data: {},
      style: 'modern',
      colors: {
        primary: '#6366f1',
        secondary: '#8b5cf6',
        text: '#ffffff',
        background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
      }
    },
    {
      id: 'ai-preset-2',
      title: 'Bold Announcement',
      description: 'High-contrast design for maximum attention',
      preview_url: `${assets.admin}/images/extensions/themes/bar-elementor/theme-two.jpg`,
      theme_data: {},
      style: 'bold',
      colors: {
        primary: '#ef4444',
        secondary: '#dc2626',
        text: '#ffffff',
        background: '#ef4444'
      }
    },
    {
      id: 'ai-preset-3',
      title: 'Minimal Notification',
      description: 'Subtle and elegant notification bar',
      preview_url: `${assets.admin}/images/extensions/themes/bar-elementor/theme-three.jpg`,
      theme_data: {},
      style: 'minimal',
      colors: {
        primary: '#64748b',
        secondary: '#475569',
        text: '#1e293b',
        background: '#f8fafc'
      }
    }
  ]

  const handleGenerateDesigns = useCallback(async () => {
    if (!prompt.trim()) return

    setIsGenerating(true)

    try {
      // Simulate AI generation delay
      await new Promise(resolve => setTimeout(resolve, 2000))

      // In real implementation, this would call your AI API
      // const response = await fetch('/wp-admin/admin-ajax.php', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      //   body: new URLSearchParams({
      //     action: 'generate_notification_designs',
      //     prompt: prompt,
      //     nonce: wpData.nonce
      //   })
      // })

      // For now, return sample presets with variations based on prompt
      const newPresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-${Date.now()}-${index}`,
        title: `${preset.title} - ${prompt.slice(0, 20)}...`,
        description: `Generated based on: "${prompt}"`
      }))

      setGeneratedPresets(newPresets)
      setCurrentPage(1)
      setHasMorePresets(true)
    } catch (error) {
      console.error('Error generating designs:', error)
    } finally {
      setIsGenerating(false)
    }
  }, [prompt, samplePresets])

  const handleLoadMore = useCallback(async () => {
    if (!hasMorePresets) return

    try {
      // Simulate loading more presets
      await new Promise(resolve => setTimeout(resolve, 1000))

      const morePresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-page${currentPage + 1}-${index}`,
        title: `${preset.title} - Variation ${currentPage + 1}`,
      }))

      setGeneratedPresets(prev => [...prev, ...morePresets])
      setCurrentPage(prev => prev + 1)

      // Simulate end of presets after 3 pages
      if (currentPage >= 2) {
        setHasMorePresets(false)
      }
    } catch (error) {
      console.error('Error loading more presets:', error)
    }
  }, [currentPage, hasMorePresets, samplePresets])

  const handleImportDesign = useCallback((preset: PresetDesign, importType: 'elementor' | 'gutenberg') => {
    // In real implementation, this would import the design to Elementor/Gutenberg
    console.log(`Importing ${preset.title} to ${importType}`)

    // Simulate import process
    alert(`Design "${preset.title}" will be imported to ${importType}. This feature will be implemented with actual import functionality.`)
  }, [])

  return (
    <div className="build-with-ai">
      <div className="build-with-ai__header">
        <div className="build-with-ai__title">
          <h3>{__('Build Notification Bar with AI', 'notificationx')}</h3>
          <p>{__('Describe your ideal notification bar and let AI generate beautiful designs for you.', 'notificationx')}</p>
        </div>
      </div>

      <div className="build-with-ai__prompt-section">
        <div className="build-with-ai__prompt-wrapper">
          <label htmlFor="ai-prompt" className="build-with-ai__prompt-label">
            {__('Describe your notification bar', 'notificationx')}
          </label>
          <textarea
            id="ai-prompt"
            className="build-with-ai__prompt-input"
            placeholder={__('E.g., "Create a modern sale notification bar with purple gradient background, white text, and a call-to-action button for Black Friday deals"', 'notificationx')}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={4}
          />
        </div>

        <button
          className="build-with-ai__generate-btn"
          onClick={handleGenerateDesigns}
          disabled={!prompt.trim() || isGenerating}
        >
          {isGenerating ? (
            <>
              <span className="build-with-ai__spinner"></span>
              {__('Generating Designs...', 'notificationx')}
            </>
          ) : (
            <>
              <svg className="build-with-ai__ai-icon" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
              {__('Generate Designs', 'notificationx')}
            </>
          )}
        </button>
      </div>

      {generatedPresets.length > 0 && (
        <div className="build-with-ai__presets-section">
          <div className="build-with-ai__presets-header">
            <h4>{__('Generated Designs', 'notificationx')}</h4>
            <span className="build-with-ai__presets-count">
              {generatedPresets.length} {__('designs generated', 'notificationx')}
            </span>
          </div>

          <div className="build-with-ai__presets-grid">
            {generatedPresets.map((preset) => (
              <div key={preset.id} className="build-with-ai__preset-card">
                <div className="build-with-ai__preset-preview">
                  <img src={preset.preview_url} alt={preset.title} />
                  <div className="build-with-ai__preset-overlay">
                    <div className="build-with-ai__preset-actions">
                      <button
                        className="build-with-ai__action-btn build-with-ai__action-btn--elementor"
                        onClick={() => handleImportDesign(preset, 'elementor')}
                        title={__('Import to Elementor', 'notificationx')}
                      >
                        <svg viewBox="0 0 24 24" fill="none">
                          <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor"/>
                          <path d="M2 17L12 22L22 17" fill="currentColor"/>
                          <path d="M2 12L12 17L22 12" fill="currentColor"/>
                        </svg>
                        {__('Elementor', 'notificationx')}
                      </button>
                      <button
                        className="build-with-ai__action-btn build-with-ai__action-btn--gutenberg"
                        onClick={() => handleImportDesign(preset, 'gutenberg')}
                        title={__('Import to Gutenberg', 'notificationx')}
                      >
                        <svg viewBox="0 0 24 24" fill="none">
                          <path d="M12 3C7.03 3 3 7.03 3 12S7.03 21 12 21 21 16.97 21 12 16.97 3 12 3ZM12 19C8.13 19 5 15.87 5 12S8.13 5 12 5 19 8.13 19 12 15.87 19 12 19Z" fill="currentColor"/>
                          <path d="M12 7V17" fill="currentColor"/>
                          <path d="M7 12H17" fill="currentColor"/>
                        </svg>
                        {__('Gutenberg', 'notificationx')}
                      </button>
                    </div>
                  </div>
                </div>

                <div className="build-with-ai__preset-info">
                  <h5 className="build-with-ai__preset-title">{preset.title}</h5>
                  <p className="build-with-ai__preset-description">{preset.description}</p>

                  <div className="build-with-ai__preset-meta">
                    <span className={`build-with-ai__preset-style build-with-ai__preset-style--${preset.style}`}>
                      {preset.style}
                    </span>
                    <div className="build-with-ai__preset-colors">
                      <span
                        className="build-with-ai__color-dot"
                        style={{ backgroundColor: preset.colors.primary }}
                        title={preset.colors.primary}
                      ></span>
                      <span
                        className="build-with-ai__color-dot"
                        style={{ backgroundColor: preset.colors.secondary }}
                        title={preset.colors.secondary}
                      ></span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {hasMorePresets && (
            <div className="build-with-ai__load-more">
              <button
                className="build-with-ai__load-more-btn"
                onClick={handleLoadMore}
              >
                {__('Load More Designs', 'notificationx')}
              </button>
            </div>
          )}
        </div>
      )}

      {generatedPresets.length === 0 && !isGenerating && (
        <div className="build-with-ai__empty-state">
          <div className="build-with-ai__empty-icon">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
          </div>
          <h4>{__('Ready to Create Amazing Designs?', 'notificationx')}</h4>
          <p>{__('Enter your requirements above and let AI generate beautiful notification bar designs tailored to your needs.', 'notificationx')}</p>
        </div>
      )}
    </div>
  )
}

export default BuildWithAI
