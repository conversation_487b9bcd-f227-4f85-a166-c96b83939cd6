import React, { useState, useCallback } from 'react'
import { __ } from '@wordpress/i18n'
import { useBuilderContext } from 'quickbuilder'
import PressbarAdminPreview from './helpers/PressbarAdminPreview'

interface PresetDesign {
  id: string
  title: string
  description: string
  config: any // NotificationX bar configuration
  data?: any // Additional data for the bar
  elementor_data?: any
  gutenberg_data?: any
  style: 'modern' | 'classic' | 'minimal' | 'bold' | 'gradient'
  colors: {
    primary: string
    secondary: string
    text: string
    background: string
  }
}

const BuildWithAI = () => {
  const builderContext = useBuilderContext()
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedPresets, setGeneratedPresets] = useState<PresetDesign[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMorePresets, setHasMorePresets] = useState(true)
  const [selectedPresetId, setSelectedPresetId] = useState<string | null>(null)
  const [isPromptModalOpen, setIsPromptModalOpen] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  
  // Predefined prompt suggestions
  const predefinedPrompts = [
    "I need notification bar designs for a big New Year celebration with 50% off on all products for the next 5 days.",
    "Give me creative notification bar ideas for a Black Friday mega sale lasting 7 days.",
    "Show me some bold and colorful banner designs for a flash sale ending tonight.",
    "I want modern, minimal banners to announce a new product launch with a signup button.",
    "Design elegant countdown banners for our Christmas holiday sale.",
    "Create playful and fun banner ideas with emojis for a summer clearance sale.",
    "Give me stylish bar designs to promote free shipping for orders above $50.",
    "Make festive banners for a Halloween special offer with spooky colors.",
    "I need simple announcement bars for a website maintenance notice — no countdown.",
    "Generate bright and colorful banners for a Diwali celebration sale with a countdown.",
    "Give me corporate-looking announcement bars for a business webinar registration.",
    "I want gradient-based banners for a Valentine's Day buy-one-get-one offer.",
    "Show me dark mode notification bars for a tech product discount.",
    "Create banners for a 24-hour flash deal on electronics with bold call-to-action buttons.",
    "Make retro-style banners for a vintage clothing store weekend sale.",
    "I need sliding text banners for promoting multiple limited-time offers.",
    "Design banners for a 10th anniversary store celebration with giveaways.",
    "Give me luxury-style bars for a premium subscription offer with gold and black colors.",
    "Make cheerful banners for a spring season sale with soft pastel colors.",
    "Create exclusive countdown bars for a pre-order campaign ending in 48 hours."
  ]

  // Sample preset data - In real implementation, this would come from AI API
  const samplePresets: PresetDesign[] = [];
  const handleGenerateDesigns = useCallback(async () => {
    if (!prompt.trim()) return
    setIsGenerating(true)

    try {
      const response = await fetch('https://shakibdev-workflow.app.n8n.cloud/webhook/c9fad03f-dba9-4f04-9624-036ce3d6b0e3', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt,
          action: 'generate_designs',
          page: 1,
          timestamp: Date.now()
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      let newPresets;
      try {
        newPresets = eval(data.output); // now it's an actual array
      } catch (e) {
        console.error("Error parsing AI presets:", e);
        newPresets = [];
      }
      samplePresets.push(...newPresets);
      const fallbackPresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-${Date.now()}-${index}`,
        title: `${preset.title} - ${prompt.slice(0, 20)}...`,
        description: `Generated based on: "${prompt}" (Fallback)`
      }))
      setGeneratedPresets(fallbackPresets)

      setCurrentPage(1)
    } catch (error) {
      console.error('Error generating designs:', error)

      // Fallback to sample presets on error
      const fallbackPresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-${Date.now()}-${index}`,
        title: `${preset.title} - ${prompt.slice(0, 20)}...`,
        description: `Generated based on: "${prompt}" (Fallback)`
      }))
      setGeneratedPresets(fallbackPresets)
      setCurrentPage(1)
      setHasMorePresets(true)
    } finally {
      setIsGenerating(false)
    }
  }, [prompt, samplePresets])

  const handleLoadMore = useCallback(async () => {
    if (!hasMorePresets || !prompt.trim() || isLoadingMore) return

    setIsLoadingMore(true)
    try {
      // Call n8n webhook for more AI-generated designs
      const response = await fetch('https://shakibdev-workflow.app.n8n.cloud/webhook/c9fad03f-dba9-4f04-9624-036ce3d6b0e3', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt,
          action: 'load_more_designs',
          page: currentPage + 1,
          timestamp: Date.now()
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
       const data = await response.json()
        let newPresets;
        try {
          newPresets = eval(data.output); // now it's an actual array
        } catch (e) {
          console.error("Error parsing AI presets:", e);
          newPresets = [];
        }
        samplePresets.push(...newPresets);
        const fallbackPresets = samplePresets.map((preset, index) => ({
          ...preset,
          id: `ai-preset-${Date.now()}-${index}`,
          title: `${preset.title} - ${prompt.slice(0, 20)}...`,
          description: `Generated based on: "${prompt}" (Fallback)`
        }))
        setGeneratedPresets(fallbackPresets)
      setCurrentPage(prev => prev + 1)
    } catch (error) {
      console.error('Error loading more presets:', error)

      // Fallback to sample presets on error
      const morePresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-page${currentPage + 1}-${index}`,
        title: `${preset.title} - Variation ${currentPage + 1} (Fallback)`,
        description: `Generated based on: "${prompt}" (Page ${currentPage + 1} - Fallback)`
      }))
      setGeneratedPresets(prev => [...prev, ...morePresets])
      setCurrentPage(prev => prev + 1)
      setHasMorePresets(currentPage < 2) // Limit fallback to 3 pages
    } finally {
      setIsLoadingMore(false)
    }
  }, [currentPage, hasMorePresets, prompt, samplePresets, isLoadingMore])

  const handleSelectPreset = useCallback((preset: PresetDesign) => {
    // Set the selected preset
    setSelectedPresetId(preset.id)

    // Apply the preset configuration to the main builder context
    if (builderContext?.setValues) {
      // Merge the preset configuration with existing values
      const updatedValues = {
        ...builderContext.values,
        ...preset.config,
        // Ensure we maintain the current nx_id if it exists
        nx_id: builderContext.values?.nx_id || preset.config.nx_id
      }

      builderContext.setValues(updatedValues)

      console.log('Applied preset configuration:', {
        presetTitle: preset.title,
        presetId: preset.id,
        appliedConfig: updatedValues
      })
    }
  }, [builderContext, setSelectedPresetId])

  const handleUsePrompt = useCallback((selectedPrompt: string) => {
    setPrompt(selectedPrompt)
    setIsPromptModalOpen(false)
  }, [])

  const handleOpenPromptModal = useCallback(() => {
    setIsPromptModalOpen(true)
  }, [])

  const handleClosePromptModal = useCallback(() => {
    setIsPromptModalOpen(false)
  }, [])

  

  return (
    <div className="build-with-ai">
      <div className="build-with-ai__header">
        <div className="build-with-ai__title">
          <h3>{__('Build Notification Bar with AI', 'notificationx')}</h3>
          <p>{__('Describe your ideal notification bar and let AI generate beautiful designs for you.', 'notificationx')}</p>
        </div>
      </div>

      <div className="build-with-ai__prompt-section">
        <div className="build-with-ai__prompt-wrapper">
          <div className="build-with-ai__prompt-header">
            <label htmlFor="ai-prompt" className="build-with-ai__prompt-label">
              {__('Describe your notification bar', 'notificationx')}
            </label>
          </div>
          <textarea
            id="ai-prompt"
            className="build-with-ai__prompt-input"
            placeholder={__('E.g., "Create a modern sale notification bar with purple gradient background, white text, and a call-to-action button for Black Friday deals"', 'notificationx')}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={4}
          />
        </div>

        {/* Predefined Prompts Section */}
        <div className="build-with-ai__predefined-prompts">
          <h4 className="build-with-ai__predefined-title">
            {__('Quick Start Prompts', 'notificationx')}
          </h4>
          <p className="build-with-ai__predefined-description">
            {__('Click on any prompt below to use it, or get inspired to create your own:', 'notificationx')}
          </p>

          <div className="build-with-ai__prompts-grid">
            {predefinedPrompts.map((promptText, index) => (
              <button
                key={index}
                className="build-with-ai__prompt-card"
                onClick={() => setPrompt(promptText)}
                title={__('Click to use this prompt', 'notificationx')}
              >
                <div className="build-with-ai__prompt-card-content">
                  {promptText}
                </div>
                <div className="build-with-ai__prompt-card-action">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </button>
            ))}
          </div>
        </div>

        <button
          className="build-with-ai__generate-btn"
          onClick={handleGenerateDesigns}
          disabled={!prompt.trim() || isGenerating}
        >
          {isGenerating ? (
            <>
              <span className="build-with-ai__spinner"></span>
              {__('Generating Designs...', 'notificationx')}
            </>
          ) : (
            <>
              <svg className="build-with-ai__ai-icon" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
              {__('Generate Designs', 'notificationx')}
            </>
          )}
        </button>
      </div>

      {generatedPresets.length > 0 && (
        <div className="build-with-ai__presets-section">
          <div className="build-with-ai__presets-list">
            {generatedPresets.map((preset) => {
              const isSelected = selectedPresetId === preset.id

              return (
                <div
                  key={preset.id}
                  className={`build-with-ai__preset-row ${isSelected ? 'build-with-ai__preset-row--selected' : ''}`}
                  onClick={() => handleSelectPreset(preset)}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      handleSelectPreset(preset)
                    }
                  }}
                  title={__('Click to apply this design to your notification bar', 'notificationx')}
                >
                  <div className="build-with-ai__preset-preview">
                    <div className="build-with-ai__live-preview">
                      <PressbarAdminPreview
                        position={preset.config.position || 'top'}
                        nxBar={{
                          config: preset.config,
                          data: preset.data || {}
                        }}
                        dispatch={() => {}} // Empty dispatch for preview only
                      />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {hasMorePresets && (
            <div className="build-with-ai__load-more">
              <button
                className="build-with-ai__load-more-btn"
                onClick={handleLoadMore}
                disabled={isLoadingMore}
              >
                {isLoadingMore ? (
                  <>
                    <span className="build-with-ai__spinner"></span>
                    {__('Loading More...', 'notificationx')}
                  </>
                ) : (
                  __('Load More Designs', 'notificationx')
                )}
              </button>
            </div>
          )}
        </div>
      )}

      {generatedPresets.length === 0 && !isGenerating && (
        <div className="build-with-ai__empty-state">
          <div className="build-with-ai__empty-icon">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
          </div>
          <h4>{__('Ready to Create Amazing Designs?', 'notificationx')}</h4>
          <p>{__('Enter your requirements above and let AI generate beautiful notification bar designs tailored to your needs.', 'notificationx')}</p>
        </div>
      )}

      {/* Prompt Selection Modal */}
      {isPromptModalOpen && (
        <div className="build-with-ai__modal-overlay" onClick={handleClosePromptModal}>
          <div className="build-with-ai__modal" onClick={(e) => e.stopPropagation()}>
            <div className="build-with-ai__modal-header">
              <h3>{__('Choose a Prompt', 'notificationx')}</h3>
              <button
                className="build-with-ai__modal-close"
                onClick={handleClosePromptModal}
                title={__('Close', 'notificationx')}
              >
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>

            <div className="build-with-ai__modal-content">
              <p className="build-with-ai__modal-description">
                {__('Select a predefined prompt to get started quickly, or use it as inspiration for your own custom prompt.', 'notificationx')}
              </p>

              <div className="build-with-ai__prompt-list">
                {predefinedPrompts.map((promptText, index) => (
                  <button
                    key={index}
                    className="build-with-ai__prompt-item"
                    onClick={() => handleUsePrompt(promptText)}
                  >
                    <div className="build-with-ai__prompt-text">
                      {promptText}
                    </div>
                    <div className="build-with-ai__prompt-action">
                      <svg viewBox="0 0 24 24" fill="none">
                        <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default BuildWithAI
