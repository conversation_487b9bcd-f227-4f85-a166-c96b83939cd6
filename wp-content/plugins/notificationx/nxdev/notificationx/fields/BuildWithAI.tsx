import React, { useState, useCallback } from 'react'
import { __ } from '@wordpress/i18n'
import PressbarAdminPreview from './helpers/PressbarAdminPreview'

interface PresetDesign {
  id: string
  title: string
  description: string
  config: any // NotificationX bar configuration
  data?: any // Additional data for the bar
  elementor_data?: any
  gutenberg_data?: any
  style: 'modern' | 'classic' | 'minimal' | 'bold' | 'gradient'
  colors: {
    primary: string
    secondary: string
    text: string
    background: string
  }
}

const BuildWithAI = () => {
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedPresets, setGeneratedPresets] = useState<PresetDesign[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMorePresets, setHasMorePresets] = useState(true)
  // const [selectedPreset, setSelectedPreset] = useState<PresetDesign | null>(null) // For future use

  // Sample preset data - In real implementation, this would come from AI API
  const samplePresets: PresetDesign[] = [
    {
      id: 'ai-preset-1',
      title: 'Modern Sale Countdown',
      description: 'Modern sale banner with countdown timer and gradient background',
      style: 'modern',
      colors: {
        primary: '#6366f1',
        secondary: '#8b5cf6',
        text: '#ffffff',
        background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
      },
      config: {
        nx_id: 'ai-preview-1',
        themes: 'theme-one',
        position: 'top',
        sticky_bar: true,
        advance_edit: true,
        enable_countdown: true,
        countdown_text: 'Limited Time Offer!',
        countdown_start_date: new Date().toISOString(),
        countdown_end_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
        evergreen_timer: false,
        press_content: '<strong>🔥 Black Friday Sale!</strong> Get up to <span style="color: #fbbf24;">70% OFF</span> on all premium plans',
        bar_content_type: 'static',
        button_text: 'Shop Now',
        button_url: '#',
        bar_bg_color: '#6366f1',
        bar_text_color: '#ffffff',
        bar_btn_bg: '#fbbf24',
        bar_btn_text_color: '#1f2937',
        bar_counter_bg: '#4f46e5',
        bar_counter_text_color: '#ffffff',
        bar_font_size: '16px',
        bar_close_position: 'right',
        bar_close_color: '#ffffff',
        bar_close_button_size: '12px'
      }
    },
    {
      id: 'ai-preset-2',
      title: 'Minimal Announcement',
      description: 'Clean minimal announcement bar with subtle styling',
      style: 'minimal',
      colors: {
        primary: '#f8fafc',
        secondary: '#e2e8f0',
        text: '#1e293b',
        background: '#f8fafc'
      },
      config: {
        nx_id: 'ai-preview-2',
        themes: 'theme-two',
        position: 'top',
        sticky_bar: true,
        advance_edit: true,
        enable_countdown: false,
        press_content: '📢 <strong>New Feature Alert:</strong> Introducing AI-powered notification bars - <em>Try it now!</em>',
        bar_content_type: 'static',
        button_text: 'Learn More',
        button_url: '#',
        bar_bg_color: '#f8fafc',
        bar_text_color: '#1e293b',
        bar_btn_bg: '#0f172a',
        bar_btn_text_color: '#ffffff',
        bar_font_size: '14px',
        bar_close_position: 'right',
        bar_close_color: '#64748b',
        bar_close_button_size: '10px'
      }
    },
    {
      id: 'ai-preset-3',
      title: 'Bold Promotional Banner',
      description: 'High-impact promotional banner with vibrant colors',
      style: 'bold',
      colors: {
        primary: '#dc2626',
        secondary: '#b91c1c',
        text: '#ffffff',
        background: '#dc2626'
      },
      config: {
        nx_id: 'ai-preview-3',
        themes: 'theme-three',
        position: 'bottom',
        sticky_bar: true,
        advance_edit: true,
        enable_countdown: true,
        countdown_text: 'Hurry! Sale ends in:',
        countdown_start_date: new Date().toISOString(),
        countdown_end_date: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(), // 6 hours from now
        evergreen_timer: false,
        press_content: '🚨 <strong>FLASH SALE!</strong> Limited time only - Don\'t miss out on these incredible deals!',
        bar_content_type: 'static',
        button_text: 'Grab Deal',
        button_url: '#',
        bar_bg_color: '#dc2626',
        bar_text_color: '#ffffff',
        bar_btn_bg: '#fbbf24',
        bar_btn_text_color: '#1f2937',
        bar_counter_bg: '#b91c1c',
        bar_counter_text_color: '#ffffff',
        bar_font_size: '16px',
        bar_close_position: 'right',
        bar_close_color: '#ffffff',
        bar_close_button_size: '14px'
      }
    },
    {
      id: 'ai-preset-4',
      title: 'Newsletter Signup',
      description: 'Elegant newsletter subscription bar with soft colors',
      style: 'classic',
      colors: {
        primary: '#059669',
        secondary: '#047857',
        text: '#ffffff',
        background: '#059669'
      },
      config: {
        nx_id: 'ai-preview-4',
        themes: 'theme-one',
        position: 'top',
        sticky_bar: false,
        advance_edit: true,
        enable_countdown: false,
        press_content: '💌 <strong>Stay Updated!</strong> Subscribe to our newsletter and get exclusive updates & offers',
        bar_content_type: 'static',
        button_text: 'Subscribe',
        button_url: '#',
        bar_bg_color: '#059669',
        bar_text_color: '#ffffff',
        bar_btn_bg: '#ffffff',
        bar_btn_text_color: '#059669',
        bar_font_size: '15px',
        bar_close_position: 'right',
        bar_close_color: '#ffffff',
        bar_close_button_size: '11px'
      }
    },
    {
      id: 'ai-preset-5',
      title: 'Sliding Content Banner',
      description: 'Dynamic banner with sliding content and multiple messages',
      style: 'gradient',
      colors: {
        primary: '#7c3aed',
        secondary: '#5b21b6',
        text: '#ffffff',
        background: 'linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%)'
      },
      config: {
        nx_id: 'ai-preview-5',
        themes: 'theme-two',
        position: 'top',
        sticky_bar: true,
        advance_edit: true,
        enable_countdown: false,
        bar_content_type: 'sliding',
        sliding_content: [
          { title: '🎉 <strong>Welcome!</strong> Discover amazing features with our AI-powered tools' },
          { title: '⚡ <strong>Fast & Reliable</strong> - Experience lightning-fast performance' },
          { title: '🔒 <strong>Secure & Private</strong> - Your data is safe with enterprise-grade security' }
        ],
        sliding_interval: 4000,
        bar_transition_style: 'slide_left',
        bar_transition_speed: 600,
        button_text: 'Get Started',
        button_url: '#',
        bar_bg_color: '#7c3aed',
        bar_text_color: '#ffffff',
        bar_btn_bg: '#fbbf24',
        bar_btn_text_color: '#1f2937',
        bar_font_size: '15px',
        bar_close_position: 'right',
        bar_close_color: '#ffffff',
        bar_close_button_size: '12px'
      }
    }
  ]

  const handleGenerateDesigns = useCallback(async () => {
    if (!prompt.trim()) return

    setIsGenerating(true)

    try {
      // Simulate AI generation delay
      await new Promise(resolve => setTimeout(resolve, 2000))

      // In real implementation, this would call your AI API
      // const response = await fetch('/wp-admin/admin-ajax.php', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      //   body: new URLSearchParams({
      //     action: 'generate_notification_designs',
      //     prompt: prompt,
      //     nonce: wpData.nonce
      //   })
      // })

      // For now, return sample presets with variations based on prompt
      const newPresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-${Date.now()}-${index}`,
        title: `${preset.title} - ${prompt.slice(0, 20)}...`,
        description: `Generated based on: "${prompt}"`
      }))

      setGeneratedPresets(newPresets)
      setCurrentPage(1)
      setHasMorePresets(true)
    } catch (error) {
      console.error('Error generating designs:', error)
    } finally {
      setIsGenerating(false)
    }
  }, [prompt, samplePresets])

  const handleLoadMore = useCallback(async () => {
    if (!hasMorePresets) return

    try {
      // Simulate loading more presets
      await new Promise(resolve => setTimeout(resolve, 1000))

      const morePresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-page${currentPage + 1}-${index}`,
        title: `${preset.title} - Variation ${currentPage + 1}`,
      }))

      setGeneratedPresets(prev => [...prev, ...morePresets])
      setCurrentPage(prev => prev + 1)

      // Simulate end of presets after 3 pages
      if (currentPage >= 2) {
        setHasMorePresets(false)
      }
    } catch (error) {
      console.error('Error loading more presets:', error)
    }
  }, [currentPage, hasMorePresets, samplePresets])

  const handleImportDesign = useCallback((preset: PresetDesign, importType: 'elementor' | 'gutenberg') => {
    // In real implementation, this would import the design to Elementor/Gutenberg
    console.log(`Importing ${preset.title} to ${importType}`)
    console.log('Configuration to import:', preset.config)

    // Show the configuration that would be imported
    const configPreview = {
      title: preset.title,
      style: preset.style,
      importType: importType,
      configuration: {
        theme: preset.config.themes,
        position: preset.config.position,
        background: preset.config.bar_bg_color,
        textColor: preset.config.bar_text_color,
        content: preset.config.press_content,
        buttonText: preset.config.button_text,
        buttonColor: preset.config.bar_btn_bg,
        countdown: preset.config.enable_countdown,
        contentType: preset.config.bar_content_type,
        slidingContent: preset.config.sliding_content
      }
    }

    alert(`Design "${preset.title}" will be imported to ${importType}.\n\nConfiguration Preview:\n${JSON.stringify(configPreview, null, 2)}`)
  }, [])

  return (
    <div className="build-with-ai">
      <div className="build-with-ai__header">
        <div className="build-with-ai__title">
          <h3>{__('Build Notification Bar with AI', 'notificationx')}</h3>
          <p>{__('Describe your ideal notification bar and let AI generate beautiful designs for you.', 'notificationx')}</p>
        </div>
      </div>

      <div className="build-with-ai__prompt-section">
        <div className="build-with-ai__prompt-wrapper">
          <label htmlFor="ai-prompt" className="build-with-ai__prompt-label">
            {__('Describe your notification bar', 'notificationx')}
          </label>
          <textarea
            id="ai-prompt"
            className="build-with-ai__prompt-input"
            placeholder={__('E.g., "Create a modern sale notification bar with purple gradient background, white text, and a call-to-action button for Black Friday deals"', 'notificationx')}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={4}
          />
        </div>

        <button
          className="build-with-ai__generate-btn"
          onClick={handleGenerateDesigns}
          disabled={!prompt.trim() || isGenerating}
        >
          {isGenerating ? (
            <>
              <span className="build-with-ai__spinner"></span>
              {__('Generating Designs...', 'notificationx')}
            </>
          ) : (
            <>
              <svg className="build-with-ai__ai-icon" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
              {__('Generate Designs', 'notificationx')}
            </>
          )}
        </button>
      </div>

      {generatedPresets.length > 0 && (
        <div className="build-with-ai__presets-section">
          <div className="build-with-ai__presets-list">
            {generatedPresets.map((preset) => (
              <div key={preset.id} className="build-with-ai__preset-row">
                <div className="build-with-ai__preset-preview">
                  <div className="build-with-ai__live-preview">
                    <PressbarAdminPreview
                      position={preset.config.position || 'top'}
                      nxBar={{
                        config: preset.config,
                        data: preset.data || {}
                      }}
                      dispatch={() => {}} // Empty dispatch for preview only
                    />
                  </div>
                </div>

                <div className="build-with-ai__preset-actions">
                  <button
                    className="build-with-ai__action-btn build-with-ai__action-btn--elementor"
                    onClick={() => handleImportDesign(preset, 'elementor')}
                    title={__('Import to Elementor', 'notificationx')}
                  >
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor"/>
                      <path d="M2 17L12 22L22 17" fill="currentColor"/>
                      <path d="M2 12L12 17L22 12" fill="currentColor"/>
                    </svg>
                    {__('Elementor', 'notificationx')}
                  </button>
                  <button
                    className="build-with-ai__action-btn build-with-ai__action-btn--gutenberg"
                    onClick={() => handleImportDesign(preset, 'gutenberg')}
                    title={__('Import to Gutenberg', 'notificationx')}
                  >
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M12 3C7.03 3 3 7.03 3 12S7.03 21 12 21 21 16.97 21 12 16.97 3 12 3ZM12 19C8.13 19 5 15.87 5 12S8.13 5 12 5 19 8.13 19 12 15.87 19 12 19Z" fill="currentColor"/>
                      <path d="M12 7V17" fill="currentColor"/>
                      <path d="M7 12H17" fill="currentColor"/>
                    </svg>
                    {__('Gutenberg', 'notificationx')}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {hasMorePresets && (
            <div className="build-with-ai__load-more">
              <button
                className="build-with-ai__load-more-btn"
                onClick={handleLoadMore}
              >
                {__('Load More Designs', 'notificationx')}
              </button>
            </div>
          )}
        </div>
      )}

      {generatedPresets.length === 0 && !isGenerating && (
        <div className="build-with-ai__empty-state">
          <div className="build-with-ai__empty-icon">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
          </div>
          <h4>{__('Ready to Create Amazing Designs?', 'notificationx')}</h4>
          <p>{__('Enter your requirements above and let AI generate beautiful notification bar designs tailored to your needs.', 'notificationx')}</p>
        </div>
      )}
    </div>
  )
}

export default BuildWithAI
