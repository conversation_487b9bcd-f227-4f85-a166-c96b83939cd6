import React, { useState, useCallback } from 'react'
import { __ } from '@wordpress/i18n'
import { useBuilderContext } from 'quickbuilder'
import PressbarAdminPreview from './helpers/PressbarAdminPreview'

interface PresetDesign {
  id: string
  title: string
  description: string
  config: any // NotificationX bar configuration
  data?: any // Additional data for the bar
  elementor_data?: any
  gutenberg_data?: any
  style: 'modern' | 'classic' | 'minimal' | 'bold' | 'gradient'
  colors: {
    primary: string
    secondary: string
    text: string
    background: string
  }
}

const BuildWithAI = () => {
  const builderContext = useBuilderContext()
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedPresets, setGeneratedPresets] = useState<PresetDesign[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMorePresets, setHasMorePresets] = useState(true)
  const [selectedPresetId, setSelectedPresetId] = useState<string | null>(null)
  const [isPromptModalOpen, setIsPromptModalOpen] = useState(false)

  // Predefined prompt suggestions
  const predefinedPrompts = [
    "I need notification bar designs for a big New Year celebration with 50% off on all products for the next 5 days.",
    "Give me creative notification bar ideas for a Black Friday mega sale lasting 7 days.",
    "Show me some bold and colorful banner designs for a flash sale ending tonight.",
    "I want modern, minimal banners to announce a new product launch with a signup button.",
    "Design elegant countdown banners for our Christmas holiday sale.",
    "Create playful and fun banner ideas with emojis for a summer clearance sale.",
    "Give me stylish bar designs to promote free shipping for orders above $50.",
    "Make festive banners for a Halloween special offer with spooky colors.",
    "I need simple announcement bars for a website maintenance notice — no countdown.",
    "Generate bright and colorful banners for a Diwali celebration sale with a countdown.",
    "Give me corporate-looking announcement bars for a business webinar registration.",
    "I want gradient-based banners for a Valentine's Day buy-one-get-one offer.",
    "Show me dark mode notification bars for a tech product discount.",
    "Create banners for a 24-hour flash deal on electronics with bold call-to-action buttons.",
    "Make retro-style banners for a vintage clothing store weekend sale.",
    "I need sliding text banners for promoting multiple limited-time offers.",
    "Design banners for a 10th anniversary store celebration with giveaways.",
    "Give me luxury-style bars for a premium subscription offer with gold and black colors.",
    "Make cheerful banners for a spring season sale with soft pastel colors.",
    "Create exclusive countdown bars for a pre-order campaign ending in 48 hours."
  ]

  // Sample preset data - In real implementation, this would come from AI API
  const samplePresets: PresetDesign[] = [
    {
      id: 'ai-preset-1',
      title: 'Modern Sale Countdown',
      description: 'Modern sale banner with countdown timer and gradient background',
      style: 'modern',
      colors: {
        primary: '#6366f1',
        secondary: '#8b5cf6',
        text: '#ffffff',
        background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
      },
      config: {
        nx_id: 'ai-preview-1',
        themes: 'theme-one',
        position: 'top',
        sticky_bar: true,
        advance_edit: true,
        enable_countdown: true,
        countdown_text: 'Limited Time Offer!',
        countdown_start_date: new Date().toISOString(),
        countdown_end_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
        evergreen_timer: false,
        press_content: '<strong>🔥 Black Friday Sale!</strong> Get up to <span style="color: #fbbf24;">70% OFF</span> on all premium plans',
        bar_content_type: 'static',
        button_text: 'Shop Now',
        button_url: '#',
        bar_bg_color: '#6366f1',
        bar_text_color: '#ffffff',
        bar_btn_bg: '#fbbf24',
        bar_btn_text_color: '#1f2937',
        bar_counter_bg: '#4f46e5',
        bar_counter_text_color: '#ffffff',
        bar_font_size: '16px',
        bar_close_position: 'right',
        bar_close_color: '#ffffff',
        bar_close_button_size: '12px'
      }
    },
    {
      id: 'ai-preset-2',
      title: 'Minimal Announcement',
      description: 'Clean minimal announcement bar with subtle styling',
      style: 'minimal',
      colors: {
        primary: '#f8fafc',
        secondary: '#e2e8f0',
        text: '#1e293b',
        background: '#f8fafc'
      },
      config: {
        nx_id: 'ai-preview-2',
        themes: 'theme-two',
        position: 'top',
        sticky_bar: true,
        advance_edit: true,
        enable_countdown: false,
        press_content: '📢 <strong>New Feature Alert:</strong> Introducing AI-powered notification bars - <em>Try it now!</em>',
        bar_content_type: 'static',
        button_text: 'Learn More',
        button_url: '#',
        bar_bg_color: '#f8fafc',
        bar_text_color: '#1e293b',
        bar_btn_bg: '#0f172a',
        bar_btn_text_color: '#ffffff',
        bar_font_size: '14px',
        bar_close_position: 'right',
        bar_close_color: '#64748b',
        bar_close_button_size: '10px'
      }
    },
    {
      id: 'ai-preset-3',
      title: 'Bold Promotional Banner',
      description: 'High-impact promotional banner with vibrant colors',
      style: 'bold',
      colors: {
        primary: '#dc2626',
        secondary: '#b91c1c',
        text: '#ffffff',
        background: '#dc2626'
      },
      config: {
        nx_id: 'ai-preview-3',
        themes: 'theme-three',
        position: 'bottom',
        sticky_bar: true,
        advance_edit: true,
        enable_countdown: true,
        countdown_text: 'Hurry! Sale ends in:',
        countdown_start_date: new Date().toISOString(),
        countdown_end_date: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(), // 6 hours from now
        evergreen_timer: false,
        press_content: '🚨 <strong>FLASH SALE!</strong> Limited time only - Don\'t miss out on these incredible deals!',
        bar_content_type: 'static',
        button_text: 'Grab Deal',
        button_url: '#',
        bar_bg_color: '#dc2626',
        bar_text_color: '#ffffff',
        bar_btn_bg: '#fbbf24',
        bar_btn_text_color: '#1f2937',
        bar_counter_bg: '#b91c1c',
        bar_counter_text_color: '#ffffff',
        bar_font_size: '16px',
        bar_close_position: 'right',
        bar_close_color: '#ffffff',
        bar_close_button_size: '14px'
      }
    },
    {
      id: 'ai-preset-4',
      title: 'Newsletter Signup',
      description: 'Elegant newsletter subscription bar with soft colors',
      style: 'classic',
      colors: {
        primary: '#059669',
        secondary: '#047857',
        text: '#ffffff',
        background: '#059669'
      },
      config: {
        nx_id: 'ai-preview-4',
        themes: 'theme-one',
        position: 'top',
        sticky_bar: false,
        advance_edit: true,
        enable_countdown: false,
        press_content: '💌 <strong>Stay Updated!</strong> Subscribe to our newsletter and get exclusive updates & offers',
        bar_content_type: 'static',
        button_text: 'Subscribe',
        button_url: '#',
        bar_bg_color: '#059669',
        bar_text_color: '#ffffff',
        bar_btn_bg: '#ffffff',
        bar_btn_text_color: '#059669',
        bar_font_size: '15px',
        bar_close_position: 'right',
        bar_close_color: '#ffffff',
        bar_close_button_size: '11px'
      }
    },
    {
      id: 'ai-preset-5',
      title: 'Sliding Content Banner',
      description: 'Dynamic banner with sliding content and multiple messages',
      style: 'gradient',
      colors: {
        primary: '#7c3aed',
        secondary: '#5b21b6',
        text: '#ffffff',
        background: 'linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%)'
      },
      config: {
        nx_id: 'ai-preview-5',
        themes: 'theme-two',
        position: 'top',
        sticky_bar: true,
        advance_edit: true,
        enable_countdown: false,
        bar_content_type: 'sliding',
        sliding_content: [
          { title: '🎉 <strong>Welcome!</strong> Discover amazing features with our AI-powered tools' },
          { title: '⚡ <strong>Fast & Reliable</strong> - Experience lightning-fast performance' },
          { title: '🔒 <strong>Secure & Private</strong> - Your data is safe with enterprise-grade security' }
        ],
        sliding_interval: 4000,
        bar_transition_style: 'slide_left',
        bar_transition_speed: 600,
        button_text: 'Get Started',
        button_url: '#',
        bar_bg_color: '#7c3aed',
        bar_text_color: '#ffffff',
        bar_btn_bg: '#fbbf24',
        bar_btn_text_color: '#1f2937',
        bar_font_size: '15px',
        bar_close_position: 'right',
        bar_close_color: '#ffffff',
        bar_close_button_size: '12px'
      }
    }
  ]

  const handleGenerateDesigns = useCallback(async () => {
    if (!prompt.trim()) return

    setIsGenerating(true)

    try {
      // Simulate AI generation delay
      await new Promise(resolve => setTimeout(resolve, 2000))

      // In real implementation, this would call your AI API
      // const response = await fetch('/wp-admin/admin-ajax.php', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      //   body: new URLSearchParams({
      //     action: 'generate_notification_designs',
      //     prompt: prompt,
      //     nonce: wpData.nonce
      //   })
      // })

      // For now, return sample presets with variations based on prompt
      const newPresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-${Date.now()}-${index}`,
        title: `${preset.title} - ${prompt.slice(0, 20)}...`,
        description: `Generated based on: "${prompt}"`
      }))

      setGeneratedPresets(newPresets)
      setCurrentPage(1)
      setHasMorePresets(true)
    } catch (error) {
      console.error('Error generating designs:', error)
    } finally {
      setIsGenerating(false)
    }
  }, [prompt, samplePresets])

  const handleLoadMore = useCallback(async () => {
    if (!hasMorePresets) return

    try {
      // Simulate loading more presets
      await new Promise(resolve => setTimeout(resolve, 1000))

      const morePresets = samplePresets.map((preset, index) => ({
        ...preset,
        id: `ai-preset-page${currentPage + 1}-${index}`,
        title: `${preset.title} - Variation ${currentPage + 1}`,
      }))

      setGeneratedPresets(prev => [...prev, ...morePresets])
      setCurrentPage(prev => prev + 1)

      // Simulate end of presets after 3 pages
      if (currentPage >= 2) {
        setHasMorePresets(false)
      }
    } catch (error) {
      console.error('Error loading more presets:', error)
    }
  }, [currentPage, hasMorePresets, samplePresets])

  const handleSelectPreset = useCallback((preset: PresetDesign) => {
    // Set the selected preset
    setSelectedPresetId(preset.id)

    // Apply the preset configuration to the main builder context
    if (builderContext?.setValues) {
      // Merge the preset configuration with existing values
      const updatedValues = {
        ...builderContext.values,
        ...preset.config,
        // Ensure we maintain the current nx_id if it exists
        nx_id: builderContext.values?.nx_id || preset.config.nx_id
      }

      builderContext.setValues(updatedValues)

      console.log('Applied preset configuration:', {
        presetTitle: preset.title,
        presetId: preset.id,
        appliedConfig: updatedValues
      })
    }
  }, [builderContext, setSelectedPresetId])

  const handleUsePrompt = useCallback((selectedPrompt: string) => {
    setPrompt(selectedPrompt)
    setIsPromptModalOpen(false)
  }, [])

  const handleOpenPromptModal = useCallback(() => {
    setIsPromptModalOpen(true)
  }, [])

  const handleClosePromptModal = useCallback(() => {
    setIsPromptModalOpen(false)
  }, [])

  return (
    <div className="build-with-ai">
      <div className="build-with-ai__header">
        <div className="build-with-ai__title">
          <h3>{__('Build Notification Bar with AI', 'notificationx')}</h3>
          <p>{__('Describe your ideal notification bar and let AI generate beautiful designs for you.', 'notificationx')}</p>
        </div>
      </div>

      <div className="build-with-ai__prompt-section">
        <div className="build-with-ai__prompt-wrapper">
          <div className="build-with-ai__prompt-header">
            <label htmlFor="ai-prompt" className="build-with-ai__prompt-label">
              {__('Describe your notification bar', 'notificationx')}
            </label>
            <button
              type="button"
              className="build-with-ai__use-prompt-btn"
              onClick={handleOpenPromptModal}
              title={__('Choose from predefined prompts', 'notificationx')}
            >
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              {__('Use Prompt', 'notificationx')}
            </button>
          </div>
          <textarea
            id="ai-prompt"
            className="build-with-ai__prompt-input"
            placeholder={__('E.g., "Create a modern sale notification bar with purple gradient background, white text, and a call-to-action button for Black Friday deals"', 'notificationx')}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={4}
          />
        </div>

        <button
          className="build-with-ai__generate-btn"
          onClick={handleGenerateDesigns}
          disabled={!prompt.trim() || isGenerating}
        >
          {isGenerating ? (
            <>
              <span className="build-with-ai__spinner"></span>
              {__('Generating Designs...', 'notificationx')}
            </>
          ) : (
            <>
              <svg className="build-with-ai__ai-icon" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
              {__('Generate Designs', 'notificationx')}
            </>
          )}
        </button>
      </div>

      {generatedPresets.length > 0 && (
        <div className="build-with-ai__presets-section">
          <div className="build-with-ai__presets-list">
            {generatedPresets.map((preset) => {
              const isSelected = selectedPresetId === preset.id

              return (
                <div
                  key={preset.id}
                  className={`build-with-ai__preset-row ${isSelected ? 'build-with-ai__preset-row--selected' : ''}`}
                  onClick={() => handleSelectPreset(preset)}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      handleSelectPreset(preset)
                    }
                  }}
                  title={__('Click to apply this design to your notification bar', 'notificationx')}
                >
                  <div className="build-with-ai__preset-preview">
                    <div className="build-with-ai__live-preview">
                      <PressbarAdminPreview
                        position={preset.config.position || 'top'}
                        nxBar={{
                          config: preset.config,
                          data: preset.data || {}
                        }}
                        dispatch={() => {}} // Empty dispatch for preview only
                      />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {hasMorePresets && (
            <div className="build-with-ai__load-more">
              <button
                className="build-with-ai__load-more-btn"
                onClick={handleLoadMore}
              >
                {__('Load More Designs', 'notificationx')}
              </button>
            </div>
          )}
        </div>
      )}

      {generatedPresets.length === 0 && !isGenerating && (
        <div className="build-with-ai__empty-state">
          <div className="build-with-ai__empty-icon">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
          </div>
          <h4>{__('Ready to Create Amazing Designs?', 'notificationx')}</h4>
          <p>{__('Enter your requirements above and let AI generate beautiful notification bar designs tailored to your needs.', 'notificationx')}</p>
        </div>
      )}

      {/* Prompt Selection Modal */}
      {isPromptModalOpen && (
        <div className="build-with-ai__modal-overlay" onClick={handleClosePromptModal}>
          <div className="build-with-ai__modal" onClick={(e) => e.stopPropagation()}>
            <div className="build-with-ai__modal-header">
              <h3>{__('Choose a Prompt', 'notificationx')}</h3>
              <button
                className="build-with-ai__modal-close"
                onClick={handleClosePromptModal}
                title={__('Close', 'notificationx')}
              >
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>

            <div className="build-with-ai__modal-content">
              <p className="build-with-ai__modal-description">
                {__('Select a predefined prompt to get started quickly, or use it as inspiration for your own custom prompt.', 'notificationx')}
              </p>

              <div className="build-with-ai__prompt-list">
                {predefinedPrompts.map((promptText, index) => (
                  <button
                    key={index}
                    className="build-with-ai__prompt-item"
                    onClick={() => handleUsePrompt(promptText)}
                  >
                    <div className="build-with-ai__prompt-text">
                      {promptText}
                    </div>
                    <div className="build-with-ai__prompt-action">
                      <svg viewBox="0 0 24 24" fill="none">
                        <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default BuildWithAI
