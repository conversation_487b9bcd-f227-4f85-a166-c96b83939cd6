// Build with AI Component Styles
.build-with-ai {
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  // Header Section
  &__header {
    margin-bottom: 32px;
    text-align: center;
  }

  &__title {
    h3 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #1e293b;
      line-height: 1.3;
    }

    p {
      margin: 0;
      font-size: 16px;
      color: #64748b;
      line-height: 1.5;
    }
  }

  // Prompt Section
  &__prompt-section {
    margin-bottom: 40px;
    padding: 24px;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
    transition: all 0.3s ease;

    &:hover {
      border-color: #6366f1;
      background: #f1f5f9;
    }
  }

  &__prompt-wrapper {
    margin-bottom: 20px;
  }

  &__prompt-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
  }

  &__prompt-input {
    width: 100%;
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
    background: #ffffff;
    resize: vertical;
    min-height: 100px;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: #6366f1;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }

  &__generate-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
  }

  &__ai-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  &__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  // Presets Section
  &__presets-section {
    margin-top: 40px;
  }

  &__presets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e5e7eb;

    h4 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
    }
  }

  &__presets-count {
    font-size: 14px;
    color: #64748b;
    background: #f1f5f9;
    padding: 4px 12px;
    border-radius: 20px;
  }

  &__presets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
  }

  &__preset-card {
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      border-color: #6366f1;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
  }

  &__preset-preview {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  &__preset-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    .build-with-ai__preset-card:hover & {
      opacity: 1;
    }
  }

  &__preset-actions {
    display: flex;
    gap: 12px;
  }

  &__action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    svg {
      width: 14px;
      height: 14px;
    }

    &--elementor {
      background: #e91e63;
      color: #ffffff;

      &:hover {
        background: #c2185b;
      }
    }

    &--gutenberg {
      background: #0073aa;
      color: #ffffff;

      &:hover {
        background: #005a87;
      }
    }
  }

  &__preset-info {
    padding: 20px;
  }

  &__preset-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.3;
  }

  &__preset-description {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.4;
  }

  &__preset-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__preset-style {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &--modern {
      background: #dbeafe;
      color: #1e40af;
    }

    &--classic {
      background: #fef3c7;
      color: #92400e;
    }

    &--minimal {
      background: #f3f4f6;
      color: #374151;
    }

    &--bold {
      background: #fee2e2;
      color: #dc2626;
    }

    &--gradient {
      background: #ede9fe;
      color: #7c3aed;
    }
  }

  &__preset-colors {
    display: flex;
    gap: 4px;
  }

  &__color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  // Load More Section
  &__load-more {
    text-align: center;
    padding: 24px 0;
  }

  &__load-more-btn {
    padding: 12px 32px;
    background: #ffffff;
    color: #6366f1;
    border: 2px solid #6366f1;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #6366f1;
      color: #ffffff;
      transform: translateY(-1px);
    }
  }

  // Empty State
  &__empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #64748b;

    h4 {
      margin: 16px 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #374151;
    }

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
      max-width: 400px;
      margin: 0 auto;
    }
  }

  &__empty-icon {
    margin-bottom: 16px;

    svg {
      width: 48px;
      height: 48px;
      color: #d1d5db;
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .build-with-ai {
    padding: 16px;

    &__presets-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    &__preset-actions {
      flex-direction: column;
      gap: 8px;
    }

    &__action-btn {
      width: 100%;
      justify-content: center;
    }

    &__presets-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
  }
}
