// Build with AI Component Styles
.build-with-ai {
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  // Header Section
  &__header {
    margin-bottom: 32px;
    text-align: center;
  }

  &__title {
    h3 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #1e293b;
      line-height: 1.3;
    }

    p {
      margin: 0;
      font-size: 16px;
      color: #64748b;
      line-height: 1.5;
    }
  }

  // Prompt Section
  &__prompt-section {
    margin-bottom: 40px;
    padding: 24px;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
    transition: all 0.3s ease;

    &:hover {
      border-color: #6366f1;
      background: #f1f5f9;
    }
  }

  &__prompt-wrapper {
    margin-bottom: 20px;
  }

  &__prompt-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
  }

  &__prompt-input {
    width: 100%;
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
    background: #ffffff;
    resize: vertical;
    min-height: 100px;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: #6366f1;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }

  &__generate-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
  }

  &__ai-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  &__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  // Presets Section
  &__presets-section {
    margin-top: 40px;
  }

  &__presets-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &__preset-row {
    display: flex;
    align-items: center;
    gap: 20px;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #6366f1;
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
    }
  }

  &__preset-preview {
    flex-shrink: 0;
    width: 200px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.02);
    }
  }

  &__preset-actions {
    display: flex;
    gap: 12px;
    margin-left: auto;
  }

  &__action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    justify-content: center;

    svg {
      width: 16px;
      height: 16px;
    }

    &--elementor {
      background: #e91e63;
      color: #ffffff;

      &:hover {
        background: #c2185b;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(233, 30, 99, 0.3);
      }
    }

    &--gutenberg {
      background: #0073aa;
      color: #ffffff;

      &:hover {
        background: #005a87;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 115, 170, 0.3);
      }
    }
  }

  // Load More Section
  &__load-more {
    text-align: center;
    padding: 24px 0;
  }

  &__load-more-btn {
    padding: 12px 32px;
    background: #ffffff;
    color: #6366f1;
    border: 2px solid #6366f1;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #6366f1;
      color: #ffffff;
      transform: translateY(-1px);
    }
  }

  // Empty State
  &__empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #64748b;

    h4 {
      margin: 16px 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #374151;
    }

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
      max-width: 400px;
      margin: 0 auto;
    }
  }

  &__empty-icon {
    margin-bottom: 16px;

    svg {
      width: 48px;
      height: 48px;
      color: #d1d5db;
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .build-with-ai {
    padding: 16px;

    &__preset-row {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    &__preset-preview {
      width: 100%;
      max-width: 300px;
      height: 180px;
      margin: 0 auto;
    }

    &__preset-actions {
      flex-direction: column;
      gap: 12px;
      width: 100%;
      margin-left: 0;
    }

    &__action-btn {
      width: 100%;
      min-width: auto;
    }
  }
}

@media (max-width: 480px) {
  .build-with-ai {
    &__preset-actions {
      gap: 8px;
    }

    &__action-btn {
      padding: 12px 16px;
      font-size: 13px;
    }
  }
}
