=== NHR Hide Admin Notice | WP Dashboard Notice Cleaner ===
Contributors: nhrrob
Tags: disable, admin, hide, notice, notification
Requires at least: 6.0
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 1.0.7
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Hide all unwanted notices and keep your dashboard clean.

== Description ==

https://www.youtube.com/watch?v=MQEORmLYjKE

Are you fed up with all the unwanted admin notices? You are not alone! 
Install this plugin and hide all unwanted notices.

`<?php echo 'Notice-Free Dashboard, Clean Dashboard!'; ?>`

**A Simplified Solution for a Cleaner WordPress Dashboard**

Tired of cluttered WordPress dashboards flooded with notices? Say hello to NHR Hide Admin Notice, the lightweight and hassle-free plugin designed to effortlessly clean up your admin interface.

### 🚀 Simple Yet Effective
With just 15 lines of code, this plugin works silently in the background, swiftly removing all those pesky notices that distract you from your work.

### ⚡ Instant Installation
No complex settings or configurations needed. Install the plugin, activate it, and voila! Your dashboard will instantly appear cleaner and more focused.

### 🎯 Enhance Productivity
By eliminating unnecessary distractions, NHR Hide Admin Notice empowers you to stay focused on what truly matters – creating exceptional content and managing your website with ease.

### 🌟 Lightweight Performance
We understand the importance of a fast-loading website. That's why our plugin is designed to have minimal impact on your site's performance, ensuring a seamless user experience for you and your visitors.

### 💬 Join Thousands of Happy Users
Join the growing community of WordPress users who trust NHR Hide Admin Notice to streamline their dashboard experience. Install it today and enjoy a clutter-free WordPress admin panel like never before!

### Visit your profile page to see all hidden notices

== Installation ==

Upload the NHR Hide Admin Notice plugin to your blog, activate it.

1, 2, 3: You're done!


== Frequently Asked Questions ==

= Does it remove WordPress admin notices? =

Yes it removes all admin notices. It just works!

= Does it require any other plugin =

No. It works as standalone plugin.

= Is NHR Hide Admin Notice compatible with all WordPress themes and plugins? =
Yes, NHR Hide Admin Notice is designed to be compatible with most WordPress themes and plugins. 
The plugin uses lightweight and non-intrusive methods to remove notices, minimizing the risk of conflicts with other plugins or themes.

= Can I customize which notices are hidden by the plugin? =
Currently, NHR Hide Admin Notice does not offer customization options to selectively hide specific notices. 
However, we may introduce such features in the future. At the moment, goal is to keep is as simple and light as possible.

= Will using NHR Hide Admin Notice impact my website's performance? =
No, NHR Hide Admin Notice is built with performance in mind and has a minimal impact on your website's speed and performance. 
The plugin's lightweight code ensures that it operates efficiently without slowing down your WordPress dashboard or affecting your site's loading times.


== Screenshots ==

1. Before installing the plugin.
2. After installing the plugin. All notices are removed.


== Changelog ==

= 1.0.7 - 29/03/2025 =
- WordPress tested up to version is updated to 6.8

= 1.0.6 - 18/10/2024 =
- WordPress tested up to version is updated to 6.7

= 1.0.5 - 26/07/2024 =
- WordPress tested up to version is updated to 6.6
- Author URI added with the org profile link
- We are just keeping things clean and simple for the time being. No feature added in this release.

= 1.0.4 - 26/03/2024 =
- WordPress tested up to version is updated to 6.5

= 1.0.3 - 02/03/2024 =
- Sometimes you may want to see if there is any admin notices. Now, you can see Admin Notices on your Profile page.

= 1.0.2 - 01/03/2024 =
- Tags updated.

= 1.0.1 - 24/02/2024 =
- Readme updated.

= 1.0.0 - 23/02/2024 =
- Initial beta release. Cheers!


== Upgrade Notice ==

= 1.0.0 =
- This is the initial release. Feel free to share any feature request at the plugin support forum page.